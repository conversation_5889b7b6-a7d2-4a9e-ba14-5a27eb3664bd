<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Eclipse Softworks</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #0A0F2C;
            color: white;
        }
        .test-section {
            background: #1a1f3a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #38BDF8;
        }
        button {
            background: #38BDF8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0284c7;
        }
        .result {
            background: #2d3748;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #38BDF8;
        }
        .success { border-left-color: #10b981; }
        .error { border-left-color: #ef4444; }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #38BDF8;
            border-radius: 5px;
            background: #2d3748;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🧪 Eclipse Softworks API Test</h1>
    <p>Use this page to test your Vercel API endpoints before going live.</p>

    <!-- CAPTCHA Verification Test -->
    <div class="test-section">
        <h2>🛡️ Test CAPTCHA Verification</h2>
        <p>Test the standalone CAPTCHA verification endpoint:</p>
        <input type="text" id="captchaToken" placeholder="Enter reCAPTCHA token (get from browser console)">
        <button onclick="testCaptcha()">Test CAPTCHA Verification</button>
        <div id="captchaResult"></div>
    </div>

    <!-- Contact Form Test -->
    <div class="test-section">
        <h2>📧 Test Contact Form</h2>
        <p>Test the complete contact form submission:</p>
        <input type="text" id="testName" placeholder="Your Name" value="Test User">
        <input type="email" id="testEmail" placeholder="Your Email" value="<EMAIL>">
        <textarea id="testMessage" placeholder="Your Message" rows="4">This is a test message from the API test page.</textarea>
        <input type="text" id="testCaptchaToken" placeholder="reCAPTCHA token">
        <button onclick="testContactForm()">Test Contact Form</button>
        <div id="contactResult"></div>
    </div>

    <!-- Instructions -->
    <div class="test-section">
        <h2>📋 How to Get reCAPTCHA Token</h2>
        <ol>
            <li>Go to your main contact form</li>
            <li>Open browser Developer Tools (F12)</li>
            <li>Go to Console tab</li>
            <li>Complete the reCAPTCHA</li>
            <li>Look for "CAPTCHA verified:" message in console</li>
            <li>Copy the token and paste it above</li>
        </ol>
    </div>

    <script>
        async function testCaptcha() {
            const token = document.getElementById('captchaToken').value;
            const resultDiv = document.getElementById('captchaResult');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="result error">Please enter a CAPTCHA token</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="result">Testing CAPTCHA verification...</div>';
                
                const response = await fetch('/api/verify-captcha', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token })
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ CAPTCHA Verification Successful!<br>
                            <strong>Hostname:</strong> ${result.hostname || 'N/A'}<br>
                            <strong>Timestamp:</strong> ${result.challenge_ts || 'N/A'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ CAPTCHA Verification Failed<br>
                            <strong>Error:</strong> ${result.error}<br>
                            <strong>Error Codes:</strong> ${result.errorCodes ? result.errorCodes.join(', ') : 'N/A'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Network Error: ${error.message}</div>`;
            }
        }

        async function testContactForm() {
            const name = document.getElementById('testName').value;
            const email = document.getElementById('testEmail').value;
            const message = document.getElementById('testMessage').value;
            const captchaToken = document.getElementById('testCaptchaToken').value;
            const resultDiv = document.getElementById('contactResult');
            
            if (!name || !email || !message || !captchaToken) {
                resultDiv.innerHTML = '<div class="result error">Please fill all fields including CAPTCHA token</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="result">Submitting contact form...</div>';
                
                const response = await fetch('/api/contact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name,
                        email,
                        message,
                        captchaToken
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Contact Form Submission Successful!<br>
                            <strong>Message:</strong> ${result.message}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Contact Form Submission Failed<br>
                            <strong>Error:</strong> ${result.error}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Network Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
