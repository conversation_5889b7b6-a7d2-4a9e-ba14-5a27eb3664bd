import { useState, lazy, Suspense } from 'react'
import { motion } from 'framer-motion'
import Navigation from './components/Navigation'
import Hero from './components/Hero'
import Footer from './components/Footer'
import SEOEnhancer from './components/SEOEnhancer'
import AccessibilityEnhancer from './components/AccessibilityEnhancer'

// Lazy load heavy components for better performance
const Services = lazy(() => import('./components/Services'))
const About = lazy(() => import('./components/About'))
const Contact = lazy(() => import('./components/Contact'))

// Import optimized loading component
import { CosmicSpinner } from './components/LoadingStates'

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-20">
    <CosmicSpinner size="lg" />
  </div>
)

function App() {
  return (
    <div className="min-h-screen bg-space-dark text-star-white relative overflow-hidden">
      {/* Starfield Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-space-gradient"></div>
        <div className="absolute inset-0 bg-nebula-gradient opacity-30"></div>
        {/* Animated stars */}
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-star-white rounded-full animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10">
        <SEOEnhancer />
        <AccessibilityEnhancer />
        <Navigation />
        <main>
          <Hero />

          <Suspense fallback={<LoadingSpinner />}>
            <Services />
          </Suspense>

          <Suspense fallback={<LoadingSpinner />}>
            <About />
          </Suspense>

          <Suspense fallback={<LoadingSpinner />}>
            <Contact />
          </Suspense>
        </main>
        <Footer />
      </div>
    </div>
  )
}

export default App
