import { useState } from 'react'
import { motion } from 'framer-motion'
import Navigation from './components/Navigation'
import Hero from './components/Hero'
import Services from './components/Services'
import About from './components/About'
import Contact from './components/Contact'
import Footer from './components/Footer'
import SEOEnhancer from './components/SEOEnhancer'
import { BannerAd, NativeAd } from './components/AdSpace'

function App() {
  return (
    <div className="min-h-screen bg-space-dark text-star-white relative overflow-hidden">
      {/* Starfield Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-space-gradient"></div>
        <div className="absolute inset-0 bg-nebula-gradient opacity-30"></div>
        {/* Animated stars */}
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-star-white rounded-full animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10">
        <SEOEnhancer />
        <Navigation />
        <main>
          <Hero />

          {/* Banner Ad after Hero */}
          <div className="py-8 px-4 sm:px-6 lg:px-8">
            <BannerAd className="max-w-4xl" />
          </div>

          <Services />

          {/* Native Ad between Services and About */}
          <div className="py-8 px-4 sm:px-6 lg:px-8">
            <NativeAd className="max-w-2xl" />
          </div>

          <About />
          <Contact />
        </main>
        <Footer />
      </div>
    </div>
  )
}

export default App
