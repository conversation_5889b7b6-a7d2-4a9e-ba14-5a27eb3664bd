import { useState, lazy, Suspense } from 'react'
import { motion } from 'framer-motion'
import Navigation from './components/Navigation'
import Hero from './components/Hero'
import Footer from './components/Footer'
import SEOEnhancer from './components/SEOEnhancer'
import PerformanceMonitor from './components/PerformanceMonitor'
import AccessibilityEnhancer from './components/AccessibilityEnhancer'

// Lazy load heavy components for better performance
const Services = lazy(() => import('./components/Services'))
const About = lazy(() => import('./components/About'))
const Contact = lazy(() => import('./components/Contact'))
const AdSpace = lazy(() => import('./components/AdSpace'))

// Import optimized loading component
import { CosmicSpinner } from './components/LoadingStates'

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-20">
    <CosmicSpinner size="lg" />
  </div>
)

function App() {
  return (
    <div className="min-h-screen bg-space-dark text-star-white relative overflow-hidden">
      {/* Starfield Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-space-gradient"></div>
        <div className="absolute inset-0 bg-nebula-gradient opacity-30"></div>
        {/* Animated stars */}
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-star-white rounded-full animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10">
        <SEOEnhancer />
        <AccessibilityEnhancer />
        <PerformanceMonitor enabled={process.env.NODE_ENV === 'development'} />
        <Navigation />
        <main>
          <Hero />

          {/* Banner Ad after Hero */}
          <Suspense fallback={<LoadingSpinner />}>
            <div className="py-8 px-4 sm:px-6 lg:px-8">
              <AdSpace size="banner" className="max-w-4xl" />
            </div>
          </Suspense>

          <Suspense fallback={<LoadingSpinner />}>
            <Services />
          </Suspense>

          {/* Native Ad between Services and About */}
          <Suspense fallback={<LoadingSpinner />}>
            <div className="py-8 px-4 sm:px-6 lg:px-8">
              <AdSpace size="native" className="max-w-2xl" />
            </div>
          </Suspense>

          <Suspense fallback={<LoadingSpinner />}>
            <About />
          </Suspense>

          <Suspense fallback={<LoadingSpinner />}>
            <Contact />
          </Suspense>
        </main>
        <Footer />
      </div>
    </div>
  )
}

export default App
