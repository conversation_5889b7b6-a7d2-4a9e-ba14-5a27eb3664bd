import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'

const AdSpace = ({ 
  size = 'banner', // 'banner', 'square', 'sidebar', 'native'
  position = 'center', // 'center', 'left', 'right'
  className = '',
  adContent = null,
  placeholder = true
}) => {
  const [isVisible, setIsVisible] = useState(true)

  // Ad size configurations
  const sizeClasses = {
    banner: 'w-full h-24 md:h-32',
    square: 'w-64 h-64',
    sidebar: 'w-48 h-96',
    native: 'w-full h-auto min-h-32',
    mobile: 'w-full h-20'
  }

  // Position classes
  const positionClasses = {
    center: 'mx-auto',
    left: 'mr-auto',
    right: 'ml-auto'
  }

  // Placeholder content for demonstration
  const PlaceholderAd = () => (
    <div className="relative w-full h-full bg-gradient-to-br from-space-dark/80 to-space-blue/20 rounded-xl border border-space-blue/30 overflow-hidden group">
      {/* Cosmic background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-space-blue/10 to-transparent animate-pulse"></div>
      
      {/* Floating particles */}
      <div className="absolute inset-0">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-space-cyan rounded-full"
            style={{
              left: `${20 + (i * 12)}%`,
              top: `${30 + (i * 8)}%`,
            }}
            animate={{
              y: [0, -10, 0],
              opacity: [0.3, 1, 0.3],
            }}
            transition={{
              duration: 2 + i * 0.3,
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>

      {/* Ad content */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full p-4 text-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
          className="space-y-2"
        >
          <div className="text-space-blue">
            <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h4 className="text-sm font-orbitron font-bold text-space-cyan">
            Stellar Advertisement Space
          </h4>
          <p className="text-xs text-star-silver font-inter">
            Your cosmic message here
          </p>
        </motion.div>
      </div>

      {/* Close button */}
      <button
        onClick={() => setIsVisible(false)}
        className="absolute top-2 right-2 w-6 h-6 bg-space-dark/80 hover:bg-space-blue/20 rounded-full flex items-center justify-center text-star-silver hover:text-space-cyan transition-colors duration-300 opacity-0 group-hover:opacity-100"
        aria-label="Close ad"
      >
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  )

  // Google AdSense component
  const GoogleAd = ({ adSlot, adFormat = 'auto' }) => {
    useEffect(() => {
      try {
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (err) {
        console.error('AdSense error:', err);
      }
    }, []);

    return (
      <ins
        className="adsbygoogle"
        style={{ display: 'block' }}
        data-ad-client="ca-pub-YOUR_PUBLISHER_ID" // Replace with your AdSense ID
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-full-width-responsive="true"
      />
    );
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className={`${sizeClasses[size]} ${positionClasses[position]} ${className}`}
    >
      {adContent ? (
        <div className="w-full h-full">
          {adContent}
        </div>
      ) : placeholder ? (
        <PlaceholderAd />
      ) : (
        <GoogleAd adSlot="YOUR_AD_SLOT_ID" />
      )}
    </motion.div>
  )
}

// Pre-configured ad components for common use cases
export const BannerAd = (props) => <AdSpace size="banner" {...props} />
export const SidebarAd = (props) => <AdSpace size="sidebar" {...props} />
export const SquareAd = (props) => <AdSpace size="square" {...props} />
export const NativeAd = (props) => <AdSpace size="native" {...props} />

export default AdSpace
