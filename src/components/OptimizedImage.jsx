import { useState, useRef, useEffect } from 'react'
import { useResponsive } from '../hooks/useResponsive'

const OptimizedImage = ({
  src,
  alt,
  className = '',
  width,
  height,
  priority = false,
  placeholder = 'blur',
  quality = 75,
  sizes,
  onLoad,
  onError,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(priority)
  const [hasError, setHasError] = useState(false)
  const imgRef = useRef(null)
  const { isMobile } = useResponsive()

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      {
        rootMargin: '50px', // Start loading 50px before the image enters viewport
        threshold: 0.1
      }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [priority, isInView])

  // Generate responsive image sources
  const generateSrcSet = (baseSrc) => {
    if (!baseSrc) return ''
    
    const sizes = [320, 640, 768, 1024, 1280, 1920]
    return sizes
      .map(size => {
        // For demo purposes, we'll use the original image
        // In a real app, you'd have different sized versions
        return `${baseSrc} ${size}w`
      })
      .join(', ')
  }

  const handleLoad = (e) => {
    setIsLoaded(true)
    onLoad?.(e)
  }

  const handleError = (e) => {
    setHasError(true)
    onError?.(e)
  }

  // Placeholder component
  const Placeholder = () => (
    <div 
      className={`bg-gradient-to-br from-space-dark/50 to-space-blue/20 animate-pulse ${className}`}
      style={{ width, height }}
    >
      <div className="w-full h-full flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-space-blue/30 border-t-space-blue rounded-full animate-spin"></div>
      </div>
    </div>
  )

  // Error fallback
  const ErrorFallback = () => (
    <div 
      className={`bg-gradient-to-br from-red-900/20 to-red-600/20 border border-red-500/30 rounded-lg flex items-center justify-center ${className}`}
      style={{ width, height }}
    >
      <div className="text-center p-4">
        <svg className="w-8 h-8 text-red-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <p className="text-sm text-red-400">Failed to load image</p>
      </div>
    </div>
  )

  if (hasError) {
    return <ErrorFallback />
  }

  if (!isInView) {
    return <Placeholder />
  }

  return (
    <div ref={imgRef} className="relative">
      {!isLoaded && placeholder === 'blur' && <Placeholder />}
      
      <img
        src={src}
        alt={alt}
        className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
        width={width}
        height={height}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
        srcSet={generateSrcSet(src)}
        sizes={sizes || (isMobile ? '100vw' : '50vw')}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          position: isLoaded ? 'static' : 'absolute',
          top: isLoaded ? 'auto' : 0,
          left: isLoaded ? 'auto' : 0,
        }}
        {...props}
      />
    </div>
  )
}

// Higher-order component for image optimization
export const withImageOptimization = (Component) => {
  return function OptimizedComponent(props) {
    const { isMobile } = useResponsive()
    
    // Automatically adjust quality based on device
    const quality = isMobile ? 60 : 75
    
    return <Component {...props} quality={quality} />
  }
}

// Preload critical images
export const preloadImage = (src, priority = false) => {
  if (typeof window === 'undefined') return

  const link = document.createElement('link')
  link.rel = priority ? 'preload' : 'prefetch'
  link.as = 'image'
  link.href = src
  
  document.head.appendChild(link)
}

// Image component with WebP support
export const WebPImage = ({ src, fallback, ...props }) => {
  const [supportsWebP, setSupportsWebP] = useState(null)

  useEffect(() => {
    // Check WebP support
    const webP = new Image()
    webP.onload = webP.onerror = () => {
      setSupportsWebP(webP.height === 2)
    }
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
  }, [])

  if (supportsWebP === null) {
    return <OptimizedImage src={fallback || src} {...props} />
  }

  const imageSrc = supportsWebP && src.includes('.webp') ? src : (fallback || src)
  
  return <OptimizedImage src={imageSrc} {...props} />
}

export default OptimizedImage
