import { useEffect } from 'react'

const SEOEnhancer = () => {
  useEffect(() => {
    // Add performance monitoring
    const addPerformanceMonitoring = () => {
      // Core Web Vitals monitoring
      if ('web-vital' in window) {
        import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
          getCLS(console.log)
          getFID(console.log)
          getFCP(console.log)
          getLCP(console.log)
          getTTFB(console.log)
        })
      }
    }

    // Add breadcrumb structured data
    const addBreadcrumbStructuredData = () => {
      const breadcrumbScript = document.createElement('script')
      breadcrumbScript.type = 'application/ld+json'
      breadcrumbScript.textContent = JSON.stringify({
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://eclipse-softworks.com/"
          },
          {
            "@type": "ListItem", 
            "position": 2,
            "name": "Services",
            "item": "https://eclipse-softworks.com/#services"
          },
          {
            "@type": "ListItem",
            "position": 3, 
            "name": "About",
            "item": "https://eclipse-softworks.com/#about"
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": "Contact", 
            "item": "https://eclipse-softworks.com/#contact"
          }
        ]
      })
      
      if (!document.querySelector('script[data-breadcrumb]')) {
        breadcrumbScript.setAttribute('data-breadcrumb', 'true')
        document.head.appendChild(breadcrumbScript)
      }
    }

    // Add FAQ structured data for better SERP features
    const addFAQStructuredData = () => {
      const faqScript = document.createElement('script')
      faqScript.type = 'application/ld+json'
      faqScript.textContent = JSON.stringify({
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
          {
            "@type": "Question",
            "name": "What services does Eclipse Softworks offer?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Eclipse Softworks offers comprehensive software development services including AI solutions, custom software development, web applications, mobile apps, cybersecurity solutions, and automation tools."
            }
          },
          {
            "@type": "Question", 
            "name": "Where is Eclipse Softworks located?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Eclipse Softworks is based in South Africa and serves clients globally with cutting-edge software development and AI solutions."
            }
          },
          {
            "@type": "Question",
            "name": "How can I contact Eclipse Softworks?",
            "acceptedAnswer": {
              "@type": "Answer", 
              "text": "You can contact Eclipse Softworks through our contact form on the website, email <NAME_EMAIL>, or connect with us on LinkedIn and other social platforms."
            }
          },
          {
            "@type": "Question",
            "name": "What makes Eclipse Softworks different?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Eclipse Softworks combines cutting-edge technology with a cosmic approach to innovation, delivering stellar software solutions with 24/7 support and a focus on transforming businesses through advanced AI and custom software development."
            }
          }
        ]
      })
      
      if (!document.querySelector('script[data-faq]')) {
        faqScript.setAttribute('data-faq', 'true')
        document.head.appendChild(faqScript)
      }
    }

    // Add local business structured data
    const addLocalBusinessData = () => {
      const localBusinessScript = document.createElement('script')
      localBusinessScript.type = 'application/ld+json'
      localBusinessScript.textContent = JSON.stringify({
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "@id": "https://eclipse-softworks.com/#organization",
        "name": "Eclipse Softworks",
        "image": "https://eclipse-softworks.com/logo.png",
        "telephone": "+27-XX-XXX-XXXX",
        "email": "<EMAIL>",
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "ZA",
          "addressRegion": "South Africa"
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": "-30.5595",
          "longitude": "22.9375"
        },
        "url": "https://eclipse-softworks.com",
        "sameAs": [
          "https://linkedin.com/company/eclipse-softworks",
          "https://facebook.com/eclipse.softworks"
        ],
        "openingHoursSpecification": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": [
            "Monday",
            "Tuesday", 
            "Wednesday",
            "Thursday",
            "Friday"
          ],
          "opens": "08:00",
          "closes": "17:00"
        },
        "priceRange": "$$",
        "currenciesAccepted": "ZAR, USD, EUR",
        "paymentAccepted": "Cash, Credit Card, Bank Transfer"
      })
      
      if (!document.querySelector('script[data-local-business]')) {
        localBusinessScript.setAttribute('data-local-business', 'true')
        document.head.appendChild(localBusinessScript)
      }
    }

    // Optimize images for SEO
    const optimizeImages = () => {
      const images = document.querySelectorAll('img:not([alt])')
      images.forEach(img => {
        if (!img.alt) {
          img.alt = 'Eclipse Softworks - Software Development and AI Solutions'
        }
      })
    }

    // Add preload hints for critical resources
    const addPreloadHints = () => {
      const preloadLinks = [
        { href: '/favicon.svg', as: 'image', type: 'image/svg+xml' },
        { href: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;500;600;700;800;900&display=swap', as: 'style' }
      ]

      preloadLinks.forEach(link => {
        const existingLink = document.querySelector(`link[href="${link.href}"]`)
        if (!existingLink) {
          const preloadLink = document.createElement('link')
          preloadLink.rel = 'preload'
          preloadLink.href = link.href
          preloadLink.as = link.as
          if (link.type) preloadLink.type = link.type
          document.head.appendChild(preloadLink)
        }
      })
    }

    // Initialize all SEO enhancements
    const initializeSEO = () => {
      addPerformanceMonitoring()
      addBreadcrumbStructuredData()
      addFAQStructuredData()
      addLocalBusinessData()
      optimizeImages()
      addPreloadHints()
    }

    // Run after DOM is loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeSEO)
    } else {
      initializeSEO()
    }

    // Cleanup function
    return () => {
      // Remove event listeners if needed
      document.removeEventListener('DOMContentLoaded', initializeSEO)
    }
  }, [])

  return null // This component doesn't render anything
}

export default SEOEnhancer
