import { useEffect, useState } from 'react'

const PerformanceMonitor = ({ enabled = false }) => {
  const [metrics, setMetrics] = useState({
    loadTime: 0,
    fcp: 0, // First Contentful Paint
    lcp: 0, // Largest Contentful Paint
    cls: 0, // Cumulative Layout Shift
    fid: 0, // First Input Delay
  })

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return

    // Measure page load time
    const measureLoadTime = () => {
      const navigation = performance.getEntriesByType('navigation')[0]
      if (navigation) {
        setMetrics(prev => ({
          ...prev,
          loadTime: navigation.loadEventEnd - navigation.loadEventStart
        }))
      }
    }

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0]
      if (fcpEntry) {
        setMetrics(prev => ({ ...prev, fcp: fcpEntry.startTime }))
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries()
            const lastEntry = entries[entries.length - 1]
            setMetrics(prev => ({ ...prev, lcp: lastEntry.startTime }))
          })
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0
            for (const entry of list.getEntries()) {
              if (!entry.hadRecentInput) {
                clsValue += entry.value
              }
            }
            setMetrics(prev => ({ ...prev, cls: clsValue }))
          })
          clsObserver.observe({ entryTypes: ['layout-shift'] })

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              setMetrics(prev => ({ ...prev, fid: entry.processingStart - entry.startTime }))
            }
          })
          fidObserver.observe({ entryTypes: ['first-input'] })
        } catch (error) {
          console.warn('Performance monitoring not fully supported:', error)
        }
      }
    }

    // Run measurements
    if (document.readyState === 'complete') {
      measureLoadTime()
      measureWebVitals()
    } else {
      window.addEventListener('load', () => {
        measureLoadTime()
        measureWebVitals()
      })
    }

    // Log performance metrics for debugging
    const logMetrics = () => {
      console.group('🚀 Eclipse Softworks Performance Metrics')
      console.log('Load Time:', metrics.loadTime.toFixed(2), 'ms')
      console.log('First Contentful Paint:', metrics.fcp.toFixed(2), 'ms')
      console.log('Largest Contentful Paint:', metrics.lcp.toFixed(2), 'ms')
      console.log('Cumulative Layout Shift:', metrics.cls.toFixed(4))
      console.log('First Input Delay:', metrics.fid.toFixed(2), 'ms')
      console.groupEnd()
    }

    // Log metrics after 3 seconds
    const timer = setTimeout(logMetrics, 3000)

    return () => clearTimeout(timer)
  }, [enabled, metrics.loadTime])

  // Don't render anything in production
  if (!enabled || process.env.NODE_ENV === 'production') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50 backdrop-blur-sm">
      <div className="text-green-400 font-bold mb-2">⚡ Performance</div>
      <div>Load: {metrics.loadTime.toFixed(0)}ms</div>
      <div>FCP: {metrics.fcp.toFixed(0)}ms</div>
      <div>LCP: {metrics.lcp.toFixed(0)}ms</div>
      <div>CLS: {metrics.cls.toFixed(3)}</div>
      <div>FID: {metrics.fid.toFixed(0)}ms</div>
    </div>
  )
}

export default PerformanceMonitor
