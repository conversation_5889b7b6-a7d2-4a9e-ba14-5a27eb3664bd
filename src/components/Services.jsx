import { motion } from 'framer-motion'
import { services } from '../data/siteData.jsx'

const Services = () => {

  return (
    <section id="services" className="py-20 px-4 sm:px-6 lg:px-8 relative">
      {/* Background cosmic elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/6 w-64 h-64 bg-space-purple/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 right-1/6 w-80 h-80 bg-space-cyan/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.h2
            className="text-4xl sm:text-5xl lg:text-6xl font-orbitron font-bold text-star-white mb-6"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Our <span className="bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent glow-text">Services</span>
          </motion.h2>
          <motion.p
            className="text-xl text-star-silver max-w-4xl mx-auto font-inter leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Navigate the cosmos of technology with our stellar solutions. From AI constellations to cybersecurity shields,
            we craft digital experiences that transcend earthly boundaries.
          </motion.p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 50, rotateX: -15 }}
              whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{
                duration: 0.8,
                delay: index * 0.15,
                type: "spring",
                stiffness: 100
              }}
              viewport={{ once: true }}
              whileHover={{
                y: -10,
                rotateX: 5,
                transition: { duration: 0.3 }
              }}
              className="space-card group cursor-pointer"
            >
              {/* Icon with gradient background */}
              <motion.div
                className={`w-16 h-16 rounded-full bg-gradient-to-br ${service.gradient} p-4 mb-6 group-hover:scale-110 transition-all duration-300 relative`}
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <div className="text-star-white w-full h-full flex items-center justify-center">
                  {service.icon}
                </div>
                {/* Orbital ring */}
                <div className="absolute inset-0 rounded-full border-2 border-space-blue/30 animate-pulse"></div>
              </motion.div>

              <h3 className="text-2xl font-orbitron font-bold text-star-white mb-4 group-hover:text-space-blue transition-colors duration-300">
                {service.title}
              </h3>

              <p className="text-star-silver leading-relaxed font-inter group-hover:text-star-white transition-colors duration-300">
                {service.description}
              </p>

              {/* Floating particles */}
              <div className="absolute top-4 right-4 w-2 h-2 bg-space-blue rounded-full animate-twinkle"></div>
              <div className="absolute bottom-6 left-6 w-1 h-1 bg-space-cyan rounded-full animate-twinkle" style={{ animationDelay: '1s' }}></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Services
