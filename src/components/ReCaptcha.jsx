import { useEffect, useRef, useState } from 'react'
import { RECAPTCHA_CONFIG } from '../config/recaptcha'

const ReCaptcha = ({
  siteKey = RECAPTCHA_CONFIG.SITE_KEY,
  onVerify,
  onExpire,
  onError,
  theme = "dark",
  size = "normal"
}) => {
  const recaptchaRef = useRef(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [widgetId, setWidgetId] = useState(null)

  useEffect(() => {
    // Check if reCAPTCHA is already loaded
    if (window.grecaptcha && window.grecaptcha.render) {
      setIsLoaded(true)
      return
    }

    // Wait for reCAPTCHA to load
    const checkRecaptcha = () => {
      if (window.grecaptcha && window.grecaptcha.render) {
        setIsLoaded(true)
      } else {
        setTimeout(checkRecaptcha, 100)
      }
    }
    
    checkRecaptcha()
  }, [])

  useEffect(() => {
    if (isLoaded && recaptchaRef.current && !widgetId) {
      try {
        const id = window.grecaptcha.render(recaptchaRef.current, {
          sitekey: siteKey,
          callback: (token) => {
            console.log('reCAPTCHA verified:', token)
            onVerify && onVerify(token)
          },
          'expired-callback': () => {
            console.log('reCAPTCHA expired')
            onExpire && onExpire()
          },
          'error-callback': () => {
            console.log('reCAPTCHA error')
            onError && onError()
          },
          theme: theme,
          size: size
        })
        setWidgetId(id)
      } catch (error) {
        console.error('Error rendering reCAPTCHA:', error)
        onError && onError(error)
      }
    }
  }, [isLoaded, siteKey, onVerify, onExpire, onError, theme, size, widgetId])

  const reset = () => {
    if (window.grecaptcha && widgetId !== null) {
      window.grecaptcha.reset(widgetId)
    }
  }

  const getResponse = () => {
    if (window.grecaptcha && widgetId !== null) {
      return window.grecaptcha.getResponse(widgetId)
    }
    return null
  }

  // Expose methods to parent component
  useEffect(() => {
    if (recaptchaRef.current) {
      recaptchaRef.current.reset = reset
      recaptchaRef.current.getResponse = getResponse
    }
  }, [widgetId])

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-space-blue"></div>
        <span className="ml-2 text-star-silver">Loading security verification...</span>
      </div>
    )
  }

  return (
    <div className="flex justify-center">
      <div 
        ref={recaptchaRef}
        className="transform scale-90 sm:scale-100 origin-center"
      />
    </div>
  )
}

export default ReCaptcha
