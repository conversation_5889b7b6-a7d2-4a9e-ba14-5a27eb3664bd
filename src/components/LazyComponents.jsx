// Lazy-loaded components for better performance
import { lazy, Suspense } from 'react'

// Loading component with cosmic theme
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-20">
    <div className="relative">
      <div className="w-16 h-16 border-4 border-space-blue/30 border-t-space-blue rounded-full animate-spin"></div>
      <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-space-purple rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
    </div>
  </div>
)

// Lazy load heavy components
export const LazyServices = lazy(() => import('./Services'))
export const LazyAbout = lazy(() => import('./About'))
export const LazyContact = lazy(() => import('./Contact'))
export const LazyAdSpace = lazy(() => import('./AdSpace'))

// HOC for wrapping lazy components with suspense
export const withSuspense = (Component, fallback = <LoadingSpinner />) => {
  return function SuspenseWrapper(props) {
    return (
      <Suspense fallback={fallback}>
        <Component {...props} />
      </Suspense>
    )
  }
}

// Pre-configured lazy components with suspense
export const ServicesWithSuspense = withSuspense(LazyServices)
export const AboutWithSuspense = withSuspense(LazyAbout)
export const ContactWithSuspense = withSuspense(LazyContact)
export const AdSpaceWithSuspense = withSuspense(LazyAdSpace)

export default {
  Services: ServicesWithSuspense,
  About: AboutWithSuspense,
  Contact: ContactWithSuspense,
  AdSpace: AdSpaceWithSuspense,
  LoadingSpinner
}
