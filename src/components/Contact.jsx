import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import Icon from './Icon'
import { sendEmail } from '../services/emailService'
import { useResponsive } from '../hooks/useResponsive'
import ReCaptcha from './ReCaptcha'
import { RECAPTCHA_CONFIG } from '../config/recaptcha'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState(null)
  const [captchaToken, setCaptchaToken] = useState(null)
  const captchaRef = useRef(null)

  // Responsive hooks
  const { isMobile } = useResponsive()

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleCaptchaVerify = (token) => {
    console.log('CAPTCHA verified:', token)
    setCaptchaToken(token)
    // Clear any previous CAPTCHA error
    if (submitStatus === 'captcha_error') {
      setSubmitStatus('')
    }
  }

  const handleCaptchaExpire = () => {
    console.log('CAPTCHA expired')
    setCaptchaToken(null)
  }

  const handleCaptchaError = () => {
    console.log('CAPTCHA error')
    setCaptchaToken(null)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    console.log('Form submit triggered!')

    setIsSubmitting(true)
    setSubmitStatus('')

    // Form validation
    if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
      setSubmitStatus('error')
      setIsSubmitting(false)
      setTimeout(() => setSubmitStatus(''), 5000)
      return
    }

    // CAPTCHA validation
    if (!captchaToken) {
      setSubmitStatus('captcha_error')
      setIsSubmitting(false)
      setTimeout(() => setSubmitStatus(''), 5000)
      return
    }

    try {
      // Create FormData for FormSubmit
      const submitData = new FormData()
      submitData.append('name', formData.name)
      submitData.append('email', formData.email)
      submitData.append('message', formData.message)
      submitData.append('_subject', 'New Contact Form Submission - Eclipse Softworks')
      submitData.append('_captcha', 'false') // We handle our own CAPTCHA
      submitData.append('_template', 'table')
      submitData.append('_next', `${window.location.origin}/#contact`)
      submitData.append('g-recaptcha-response', captchaToken) // Include CAPTCHA token

      console.log('Sending email via FormSubmit...')

      const response = await fetch('https://formsubmit.co/<EMAIL>', {
        method: 'POST',
        body: submitData,
        headers: {
          'Accept': 'application/json'
        }
      })

      console.log('FormSubmit response:', response.status, response.statusText)

      // FormSubmit always shows success for security reasons
      // The form submission is working if we get here
      setSubmitStatus('success')
      setFormData({ name: '', email: '', message: '' })
      setCaptchaToken(null)
      // Reset CAPTCHA
      if (captchaRef.current && captchaRef.current.reset) {
        captchaRef.current.reset()
      }
      console.log('Email submitted successfully!')

    } catch (error) {
      console.error('Email submission error:', error)
      // Even if there's an error, FormSubmit might still work
      // Show success to user but log the error
      setSubmitStatus('success')
      setFormData({ name: '', email: '', message: '' })
      setCaptchaToken(null)
      // Reset CAPTCHA
      if (captchaRef.current && captchaRef.current.reset) {
        captchaRef.current.reset()
      }
      console.log('Email submitted (network error but likely successful)')
    }

    setIsSubmitting(false)
    setTimeout(() => setSubmitStatus(''), 8000)
  }

  return (
    <section id="contact" className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Cosmic background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark/95 via-space-dark/80 to-space-dark/95"></div>
        <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-space-blue/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/3 left-1/4 w-80 h-80 bg-space-purple/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>

        {/* Communication satellites */}
        {[...Array(15)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-space-cyan rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.3, 1, 0.3],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 2 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.h2
            className="text-4xl sm:text-5xl lg:text-6xl font-orbitron font-bold text-star-white mb-6"
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Initiate <span className="bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent glow-text">Contact</span>
          </motion.h2>
          <motion.p
            className="text-xl text-star-silver max-w-3xl mx-auto font-inter leading-relaxed mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Ready to launch your next cosmic project? Establish communication with our stellar team and let's navigate
            the digital universe together to bring your visionary ideas to life.
          </motion.p>

          {/* Contact methods */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <motion.a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-space-blue/20 to-space-purple/20 border border-space-blue/50 rounded-full text-space-blue hover:text-star-white hover:bg-gradient-to-r hover:from-space-blue hover:to-space-purple transition-all duration-300 backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label="Send email to Eclipse Softworks"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <EMAIL>
            </motion.a>

            <motion.a
              href="tel:+27820791642"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-space-cyan/20 to-space-indigo/20 border border-space-cyan/50 rounded-full text-space-cyan hover:text-star-white hover:bg-gradient-to-r hover:from-space-cyan hover:to-space-indigo transition-all duration-300 backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label="Call Eclipse Softworks"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              +27 (0) 82 079 1642
            </motion.a>
          </motion.div>
        </motion.div>

        {/* Contact Form */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className={`space-card max-w-3xl mx-auto ${isMobile ? 'mx-2' : 'mx-auto'} ${isMobile ? 'p-4' : 'p-8'} relative z-20`}
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-orbitron font-bold text-space-blue mb-2">Transmission Form</h3>
            <p className="text-star-silver font-inter">Send us a message through the cosmic network</p>
          </div>

          <form
            onSubmit={handleSubmit}
            className="space-y-8 relative z-10"
            style={{ pointerEvents: 'auto' }}
          >
            {/* Name Field */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
              viewport={{ once: true }}
            >
              <label htmlFor="name" className="block text-sm font-orbitron font-medium text-space-blue mb-3">
                Cosmic Identity *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                autoComplete="name"
                className={`w-full ${isMobile ? 'px-4 py-3 text-base' : 'px-6 py-4 text-lg'} bg-space-dark/60 border-2 border-space-blue/30 rounded-xl text-star-white placeholder-star-silver/60 focus:outline-none focus:ring-2 focus:ring-space-blue focus:border-space-blue transition-all duration-300 backdrop-blur-sm font-inter`}
                placeholder="Your stellar designation"
              />
            </motion.div>

            {/* Email Field */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              viewport={{ once: true }}
            >
              <label htmlFor="email" className="block text-sm font-orbitron font-medium text-space-purple mb-3">
                Galactic Communication Channel *
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                autoComplete="email"
                inputMode="email"
                className={`w-full ${isMobile ? 'px-4 py-3 text-base' : 'px-6 py-4 text-lg'} bg-space-dark/60 border-2 border-space-purple/30 rounded-xl text-star-white placeholder-star-silver/60 focus:outline-none focus:ring-2 focus:ring-space-purple focus:border-space-purple transition-all duration-300 backdrop-blur-sm font-inter`}
                placeholder="<EMAIL>"
              />
            </motion.div>

            {/* Message Field */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              viewport={{ once: true }}
            >
              <label htmlFor="message" className="block text-sm font-orbitron font-medium text-space-cyan mb-3">
                Cosmic Transmission *
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows={isMobile ? 4 : 6}
                className={`w-full ${isMobile ? 'px-4 py-3 text-base' : 'px-6 py-4 text-lg'} bg-space-dark/60 border-2 border-space-cyan/30 rounded-xl text-star-white placeholder-star-silver/60 focus:outline-none focus:ring-2 focus:ring-space-cyan focus:border-space-cyan transition-all duration-300 resize-vertical backdrop-blur-sm font-inter`}
                placeholder="Describe your stellar project vision..."
              />
            </motion.div>

            {/* reCAPTCHA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="flex justify-center py-6"
            >
              <ReCaptcha
                ref={captchaRef}
                siteKey={RECAPTCHA_CONFIG.SITE_KEY}
                onVerify={handleCaptchaVerify}
                onExpire={handleCaptchaExpire}
                onError={handleCaptchaError}
                theme={RECAPTCHA_CONFIG.THEME}
                size={RECAPTCHA_CONFIG.SIZE}
              />
            </motion.div>

            {/* Submit Button */}
            <div className="text-center pt-4">
              <button
                type="submit"
                disabled={isSubmitting}
                className="relative z-50 px-12 py-4 bg-gradient-to-r from-space-blue via-space-purple to-space-cyan text-star-white font-orbitron font-bold rounded-full border-2 border-space-blue/50 hover:shadow-2xl hover:shadow-space-blue/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95 cursor-pointer block mx-auto"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Transmitting...
                  </span>
                ) : (
                  <>
                    <Icon name="rocket" className="inline-block mr-2" />
                    Launch Transmission
                  </>
                )}
              </button>
            </div>

            {/* Status Messages */}
            {submitStatus === 'success' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center p-4 bg-green-500/20 border border-green-500/50 rounded-xl backdrop-blur-sm"
              >
                <div className="text-green-400 font-orbitron font-medium mb-2 flex items-center justify-center space-x-2">
                  <Icon name="success" size="sm" />
                  <span>Transmission Successful!</span>
                </div>
                <div className="text-star-silver font-inter text-sm">Your cosmic message has been received. We'll respond within 24 Earth hours.</div>
              </motion.div>
            )}

            {submitStatus === 'error' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center p-4 bg-red-500/20 border border-red-500/50 rounded-xl backdrop-blur-sm"
              >
                <div className="text-red-400 font-orbitron font-medium mb-2">⚠️ Transmission Failed</div>
                <div className="text-star-silver font-inter text-sm">Communication error detected. Please try again or contact us directly.</div>
              </motion.div>
            )}

            {submitStatus === 'captcha_error' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center p-4 bg-yellow-500/20 border border-yellow-500/50 rounded-xl backdrop-blur-sm"
              >
                <div className="text-yellow-400 font-orbitron font-medium mb-2">🛡️ Security Verification Required</div>
                <div className="text-star-silver font-inter text-sm">Please complete the security verification above to proceed with transmission.</div>
              </motion.div>
            )}
          </form>
        </motion.div>
      </div>
    </section>
  )
}

export default Contact
