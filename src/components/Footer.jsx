import { motion } from 'framer-motion'
import EclipseLogo from './EclipseLogo'
import Icon from './Icon'
import { socialLinks, footerLinks, companyInfo } from '../data/siteData.jsx'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  // email to social links from data file
  const allSocialLinks = [
    ...socialLinks,
    {
      href: `mailto:${companyInfo.email}`,
      label: 'Email',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      gradient: 'from-space-blue to-space-cyan',
    },
  ]

  return (
    <footer className="relative overflow-hidden bg-gradient-to-br from-space-dark via-space-dark/95 to-space-dark border-t border-space-blue/30 py-16 px-4 sm:px-6 lg:px-8">
      {/* Cosmic background */}
      <div className="absolute inset-0">
        <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-space-purple/10 rounded-full blur-3xl"></div>
        <div className="absolute top-0 right-1/3 w-80 h-80 bg-space-cyan/10 rounded-full blur-3xl"></div>

        {/* Constellation pattern */}
        {[...Array(30)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-star-white rounded-full animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-2 space-y-6"
          >
            <div className="flex items-center space-x-4">
              <EclipseLogo size="medium" />
              <div>
                <h3 className="text-2xl font-orbitron font-bold bg-gradient-to-r from-space-blue to-space-purple bg-clip-text text-transparent">
                  Eclipse Softworks
                </h3>
                <p className="text-star-silver font-inter text-sm">(Pty) Ltd</p>
              </div>
            </div>
            <p className="text-star-silver leading-relaxed font-inter max-w-md">
              Navigating the digital cosmos to deliver innovative software and AI solutions.
              We transform visionary ideas into stellar realities that transcend earthly boundaries.
            </p>
            <div className="flex items-center space-x-2 text-space-cyan">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span className="text-sm font-inter">South Africa & Beyond</span>
            </div>
          </motion.div>

          {/* Navigation Links */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h4 className="text-lg font-orbitron font-bold text-space-blue">Navigation</h4>
            <ul className="space-y-3">
              {footerLinks.map((link, index) => (
                <motion.li
                  key={link.href}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <a
                    href={link.href}
                    className="flex items-center space-x-2 text-star-silver hover:text-space-cyan transition-all duration-300 group font-inter"
                  >
                    <span className="text-space-cyan group-hover:scale-110 transition-transform duration-300">
                      <Icon name={link.icon} size="sm" />
                    </span>
                    <span className="group-hover:translate-x-1 transition-transform duration-300">{link.label}</span>
                  </a>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Social Links */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h4 className="text-lg font-orbitron font-bold text-space-purple">Connect</h4>
            <div className="grid grid-cols-2 gap-3">
              {allSocialLinks.map((social, index) => (
                <motion.a
                  key={social.label}
                  href={social.href}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.1, y: -2 }}
                  className={`flex items-center justify-center p-3 bg-gradient-to-br ${social.gradient} rounded-xl text-star-white hover:shadow-lg hover:shadow-space-blue/20 transition-all duration-300 group`}
                  aria-label={social.label}
                >
                  <div className="group-hover:rotate-12 transition-transform duration-300">
                    {social.icon}
                  </div>
                </motion.a>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="border-t border-space-blue/20 mt-12 pt-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <p className="text-star-silver font-inter text-sm">
              © {currentYear} Eclipse Softworks (Pty) Ltd. All rights reserved.
            </p>

            {/* Additional Links */}
            <div className="flex items-center space-x-6 text-sm">
              <a href="#privacy" className="text-star-silver hover:text-space-cyan transition-colors duration-300 font-inter">
                Privacy Policy
              </a>
              <a href="#terms" className="text-star-silver hover:text-space-cyan transition-colors duration-300 font-inter">
                Terms of Service
              </a>
              <span className="text-space-blue font-orbitron text-xs flex items-center space-x-1">
                <span>Made with</span>
                <Icon name="heart" size="xs" className="text-red-400" />
                <span>in South Africa</span>
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
