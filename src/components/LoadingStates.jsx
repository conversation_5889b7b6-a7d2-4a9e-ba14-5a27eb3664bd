import { motion } from 'framer-motion'
import { useReducedMotion } from '../hooks/useResponsive'

// Cosmic loading spinner
export const CosmicSpinner = ({ size = 'md', className = '' }) => {
  const prefersReducedMotion = useReducedMotion()
  
  const sizes = {
    sm: 'w-6 h-6',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }

  const spinnerVariants = {
    animate: {
      rotate: prefersReducedMotion ? 0 : 360,
      transition: {
        duration: prefersReducedMotion ? 0 : 2,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'linear'
      }
    }
  }

  const orbitVariants = {
    animate: {
      rotate: prefersReducedMotion ? 0 : -360,
      transition: {
        duration: prefersReducedMotion ? 0 : 1.5,
        repeat: prefersReducedMotion ? 0 : Infinity,
        ease: 'linear'
      }
    }
  }

  return (
    <div className={`relative ${sizes[size]} ${className}`}>
      {/* Main ring */}
      <motion.div
        className="absolute inset-0 border-4 border-space-blue/30 border-t-space-blue rounded-full"
        variants={spinnerVariants}
        animate="animate"
      />
      
      {/* Orbiting element */}
      <motion.div
        className="absolute inset-0"
        variants={orbitVariants}
        animate="animate"
      >
        <div className="absolute top-0 left-1/2 w-2 h-2 bg-space-purple rounded-full transform -translate-x-1/2 -translate-y-1"></div>
      </motion.div>
      
      {/* Center dot */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-2 h-2 bg-space-cyan rounded-full animate-pulse"></div>
      </div>
    </div>
  )
}

// Skeleton loader for text content
export const TextSkeleton = ({ lines = 3, className = '' }) => {
  const prefersReducedMotion = useReducedMotion()
  
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={`h-4 bg-gradient-to-r from-space-dark/50 via-space-blue/20 to-space-dark/50 rounded ${
            i === lines - 1 ? 'w-3/4' : 'w-full'
          } ${prefersReducedMotion ? '' : 'animate-pulse'}`}
        />
      ))}
    </div>
  )
}

// Card skeleton loader
export const CardSkeleton = ({ className = '' }) => {
  const prefersReducedMotion = useReducedMotion()
  
  return (
    <div className={`space-card ${className}`}>
      <div className={`w-full h-48 bg-gradient-to-br from-space-dark/50 to-space-blue/20 rounded-lg mb-4 ${prefersReducedMotion ? '' : 'animate-pulse'}`} />
      <div className="space-y-3">
        <div className={`h-6 bg-gradient-to-r from-space-dark/50 via-space-blue/20 to-space-dark/50 rounded w-3/4 ${prefersReducedMotion ? '' : 'animate-pulse'}`} />
        <TextSkeleton lines={2} />
      </div>
    </div>
  )
}

// Button loading state
export const ButtonLoader = ({ children, isLoading, disabled, className = '', ...props }) => {
  return (
    <button
      className={`relative ${className} ${isLoading ? 'cursor-not-allowed' : ''}`}
      disabled={disabled || isLoading}
      {...props}
    >
      <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <CosmicSpinner size="sm" />
        </div>
      )}
    </button>
  )
}

// Page loading overlay
export const PageLoader = ({ isLoading, message = 'Initializing cosmic systems...' }) => {
  const prefersReducedMotion = useReducedMotion()
  
  if (!isLoading) return null

  return (
    <motion.div
      className="fixed inset-0 bg-space-dark/90 backdrop-blur-sm z-50 flex items-center justify-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: prefersReducedMotion ? 0 : 0.3 }}
    >
      <div className="text-center">
        <CosmicSpinner size="xl" className="mx-auto mb-6" />
        <motion.p
          className="text-star-silver font-inter text-lg"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: prefersReducedMotion ? 0 : 0.5 }}
        >
          {message}
        </motion.p>
        
        {/* Animated dots */}
        <motion.div
          className="flex justify-center space-x-1 mt-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: prefersReducedMotion ? 0 : 1 }}
        >
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-space-blue rounded-full"
              animate={prefersReducedMotion ? {} : {
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </motion.div>
      </div>
    </motion.div>
  )
}

// Progress bar component
export const ProgressBar = ({ progress = 0, className = '', showPercentage = true }) => {
  const prefersReducedMotion = useReducedMotion()
  
  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm text-star-silver">Loading...</span>
        {showPercentage && (
          <span className="text-sm text-space-blue font-mono">{Math.round(progress)}%</span>
        )}
      </div>
      
      <div className="w-full bg-space-dark/50 rounded-full h-2 overflow-hidden">
        <motion.div
          className="h-full bg-gradient-to-r from-space-blue to-space-purple rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: prefersReducedMotion ? 0 : 0.5, ease: 'easeOut' }}
        />
      </div>
    </div>
  )
}

// Lazy loading wrapper with error boundary
export const LazyWrapper = ({ children, fallback, error }) => {
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-4">
          <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-orbitron text-red-400 mb-2">System Error</h3>
        <p className="text-star-silver">Failed to load component. Please try refreshing the page.</p>
      </div>
    )
  }

  return fallback || (
    <div className="flex items-center justify-center py-20">
      <CosmicSpinner size="lg" />
    </div>
  )
}

export default {
  CosmicSpinner,
  TextSkeleton,
  CardSkeleton,
  ButtonLoader,
  PageLoader,
  ProgressBar,
  LazyWrapper
}
