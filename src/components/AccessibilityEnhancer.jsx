import { useEffect, useState } from 'react'
import { useReducedMotion } from '../hooks/useResponsive'

const AccessibilityEnhancer = () => {
  const [focusVisible, setFocusVisible] = useState(false)
  const [highContrast, setHighContrast] = useState(false)
  const prefersReducedMotion = useReducedMotion()

  useEffect(() => {
    // Add focus-visible polyfill behavior
    const handleKeyDown = (e) => {
      if (e.key === 'Tab') {
        setFocusVisible(true)
      }
    }

    const handleMouseDown = () => {
      setFocusVisible(false)
    }

    // Check for high contrast preference
    const checkHighContrast = () => {
      if (window.matchMedia) {
        const highContrastQuery = window.matchMedia('(prefers-contrast: high)')
        setHighContrast(highContrastQuery.matches)
        
        highContrastQuery.addEventListener('change', (e) => {
          setHighContrast(e.matches)
        })
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('mousedown', handleMouseDown)
    checkHighContrast()

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('mousedown', handleMouseDown)
    }
  }, [])

  useEffect(() => {
    // Apply accessibility classes to document
    const classes = []
    
    if (focusVisible) classes.push('focus-visible')
    if (highContrast) classes.push('high-contrast')
    if (prefersReducedMotion) classes.push('reduced-motion')

    // Add classes to document
    document.documentElement.className = document.documentElement.className
      .replace(/focus-visible|high-contrast|reduced-motion/g, '')
      .trim()
    
    if (classes.length > 0) {
      document.documentElement.className += ' ' + classes.join(' ')
    }
  }, [focusVisible, highContrast, prefersReducedMotion])

  useEffect(() => {
    // Add skip link functionality
    const skipLink = document.createElement('a')
    skipLink.href = '#main-content'
    skipLink.textContent = 'Skip to main content'
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-space-blue focus:text-white focus:rounded'
    skipLink.style.cssText = `
      position: absolute;
      left: -10000px;
      top: auto;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `
    
    const focusStyle = `
      position: absolute !important;
      left: 4px !important;
      top: 4px !important;
      width: auto !important;
      height: auto !important;
      overflow: visible !important;
      background: #3B82F6 !important;
      color: white !important;
      padding: 8px 16px !important;
      border-radius: 4px !important;
      z-index: 9999 !important;
      text-decoration: none !important;
    `

    skipLink.addEventListener('focus', () => {
      skipLink.style.cssText = focusStyle
    })

    skipLink.addEventListener('blur', () => {
      skipLink.style.cssText = `
        position: absolute;
        left: -10000px;
        top: auto;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `
    })

    document.body.insertBefore(skipLink, document.body.firstChild)

    return () => {
      if (skipLink.parentNode) {
        skipLink.parentNode.removeChild(skipLink)
      }
    }
  }, [])

  useEffect(() => {
    // Add main content landmark if it doesn't exist
    const main = document.querySelector('main')
    if (main && !main.id) {
      main.id = 'main-content'
    }
  }, [])

  // Add CSS for accessibility enhancements
  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      /* Focus visible styles */
      .focus-visible *:focus {
        outline: 2px solid #3B82F6 !important;
        outline-offset: 2px !important;
      }

      /* High contrast mode */
      .high-contrast {
        filter: contrast(150%) !important;
      }

      .high-contrast * {
        text-shadow: none !important;
        box-shadow: none !important;
      }

      /* Reduced motion */
      .reduced-motion *,
      .reduced-motion *::before,
      .reduced-motion *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
      }

      /* Screen reader only class */
      .sr-only {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
      }

      .focus\\:not-sr-only:focus {
        position: static !important;
        width: auto !important;
        height: auto !important;
        padding: 0.5rem 1rem !important;
        margin: 0 !important;
        overflow: visible !important;
        clip: auto !important;
        white-space: normal !important;
      }

      /* Ensure sufficient color contrast */
      @media (prefers-contrast: high) {
        :root {
          --space-blue: #0066FF;
          --space-purple: #8B00FF;
          --space-cyan: #00FFFF;
          --star-white: #FFFFFF;
          --star-silver: #E5E5E5;
        }
      }
    `
    
    document.head.appendChild(style)

    return () => {
      if (style.parentNode) {
        style.parentNode.removeChild(style)
      }
    }
  }, [])

  return null // This component doesn't render anything visible
}

export default AccessibilityEnhancer
