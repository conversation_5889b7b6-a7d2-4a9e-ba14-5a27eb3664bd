// reCAPTCHA Configuration
// Get your site key from: https://www.google.com/recaptcha/admin

export const RECAPTCHA_CONFIG = {
  // Your actual reCAPTCHA site key (public - safe to use in frontend)
  SITE_KEY: "6LdOVXYrAAAAADjDuymHEn4qmh2niAAeekGLwEPg",

  // Theme options: 'light' or 'dark'
  THEME: "dark",

  // Size options: 'normal', 'compact'
  SIZE: "normal"
}

// Instructions for setting up reCAPTCHA:
// 1. Go to https://www.google.com/recaptcha/admin
// 2. Create a new site
// 3. Choose reCAPTCHA v2 "I'm not a robot" checkbox
// 4. Add your domain(s) (e.g., eclipse-softworks.com, localhost)
// 5. Copy the site key and replace the SITE_KEY above
// 6. The secret key should be used on your backend server (not in frontend code)
