@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #0A0F2C 0%, #1E1B4B 50%, #312E81 100%);
    background-attachment: fixed;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #0A0F2C;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #38BDF8, #8B5CF6);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0EA5E9, #7C3AED);
  }
}

@layer components {
  .btn-primary {
    background-image: linear-gradient(to right, var(--tw-gradient-stops, #0A0F2C, #312E81));
    color: #F8FAFC; /* Example for text-star-white */
    font-family: 'Orbitron', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
    font-weight: 600;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    padding-left: 2rem;
    padding-right: 2rem;
    border-radius: 9999px;
    transition: all 0.3s;
    box-shadow: 0 10px 15px -3px rgba(56, 189, 248, 0.25);
    outline: none;
  }
  .btn-primary:hover {
    background-image: linear-gradient(to right, #06b6d4, #6366f1);
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(56, 189, 248, 0.25);
  }
  .btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px #38BDF8, 0 10px 15px -3px rgba(56, 189, 248, 0.25);
  }

  .btn-secondary {
    border-width: 2px;
    border-color: #1E40AF; /* Example for space-blue */
    color: #1E40AF; /* Example for space-blue */
    background: transparent;
    font-family: 'Orbitron', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-weight: 600;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    padding-left: 2rem;
    padding-right: 2rem;
    border-radius: 9999px;
    transition: all 0.3s;
    outline: none;
    backdrop-filter: blur(4px);
  }
  .btn-secondary:hover {
    background: #1E40AF; /* Example for space-blue */
    color: #F8FAFC; /* Example for star-white */
  }
  .btn-secondary:focus {
    outline: none;
    box-shadow: 0 0 0 2px #1E40AF, 0 0 0 4px rgba(30, 64, 175, 0.5);
  }

  .card {
    background: rgba(10, 15, 44, 0.6); /* Example for space-dark/60 */
    backdrop-filter: blur(8px);
    border: 1px solid rgba(30, 64, 175, 0.3); /* space-blue/30 */
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.3s;
  }
  .card:hover {
    border-color: rgba(30, 64, 175, 0.6); /* space-blue/60 */
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(30, 64, 175, 0.1);
  }

  .space-card {
    background: linear-gradient(135deg, rgba(10, 15, 44, 0.8) 0%, rgba(10, 15, 44, 0.6) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(30, 64, 175, 0.2); /* space-blue/20 */
    border-radius: 1rem;
    padding: 2rem;
    transition: all 0.5s;
    position: relative;
    overflow: hidden;
  }
  .space-card:hover {
    border-color: rgba(30, 64, 175, 0.5); /* space-blue/50 */
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(30, 64, 175, 0.2);
  }

  .space-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(56, 189, 248, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .space-card:hover::before {
    opacity: 1;
  }

  .glow-text {
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.5), 0 0 20px rgba(56, 189, 248, 0.3), 0 0 30px rgba(56, 189, 248, 0.1);
  }

  .nebula-bg {
    background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
  }
}
