// Eclipse Softworks - Dynamic Site Data
// Update this file to modify company stats, services, social links, and other dynamic content

export const companyStats = [
  { number: '0', label: 'Stellar Projects', icon: 'rocket' },
  { number: '24/7', label: 'Cosmic Support', icon: 'galaxy' },
  { number: '0★', label: 'Galaxy Rating', icon: 'star' },
  { number: '∞', label: 'Innovation Potential', icon: 'sparkle' }
]

export const socialLinks = [
  {
    href: 'https://linkedin.com/company/eclipse-softworks',
    label: 'LinkedIn',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
      </svg>
    ),
    gradient: 'from-blue-500 to-blue-600',
  },
  {
    href: 'https://github.com/eclipse-softworks',
    label: 'GitHub',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
      </svg>
    ),
    gradient: 'from-gray-700 to-gray-900',
  },
  {
    href: 'https://facebook.com/leobullet',
    label: 'Facebook',
    icon: (
      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
      </svg>
    ),
    gradient: 'from-blue-600 to-blue-800',
  },
]

export const services = [
  {
    title: 'AI Solutions',
    description: 'Advanced artificial intelligence and machine learning systems that navigate the cosmos of data to unlock stellar insights.',
    icon: (
      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    ),
    gradient: 'from-space-blue to-space-cyan',
  },
  {
    title: 'Web & App Development',
    description: 'Cosmic web experiences and stellar mobile applications that transcend digital boundaries with cutting-edge technology.',
    icon: (
      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
      </svg>
    ),
    gradient: 'from-space-purple to-nebula-purple',
  },
  {
    title: 'Cloud Infrastructure',
    description: 'Scalable cloud architectures that orbit your business needs with enterprise-grade security and cosmic reliability.',
    icon: (
      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
      </svg>
    ),
    gradient: 'from-space-cyan to-space-blue',
  },
  {
    title: 'Cybersecurity',
    description: 'Galactic-grade security shields protecting your digital assets from cosmic threats and interstellar vulnerabilities.',
    icon: (
      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
      </svg>
    ),
    gradient: 'from-nebula-purple to-space-purple',
  },
  {
    title: 'Data Analytics',
    description: 'Transform raw data into stellar insights with our cosmic analytics platforms and interstellar business intelligence.',
    icon: (
      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    gradient: 'from-space-blue to-space-purple',
  },
  {
    title: 'Digital Transformation',
    description: 'Navigate your organization through the digital cosmos with our comprehensive transformation strategies and stellar solutions.',
    icon: (
      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
      </svg>
    ),
    gradient: 'from-space-cyan to-nebula-purple',
  },
]

// Company information that might change over time
export const companyInfo = {
  name: 'Eclipse Softworks',
  legalName: 'Eclipse Softworks (Pty) Ltd',
  tagline: 'Navigating the cosmos of technology',
  description: 'Eclipse Softworks (Pty) Ltd is a South African software and AI innovation company dedicated to building next-generation tools and digital systems. We navigate the cosmos of technology to deliver solutions that transcend earthly boundaries.',
  email: '<EMAIL>',
  phone: '+27-82-079-1642',
  country: 'South Africa',
  website: 'https://eclipse-softworks.com',
  founded: '2024',
  // Update these URLs as needed
  socialUrls: {
    linkedin: 'https://linkedin.com/company/eclipse-softworks',
    github: 'https://github.com/eclipse-softworks',
    facebook: 'https://facebook.com/leobullet'
  }
}

// Navigation links
export const navigationLinks = [
  { href: '#home', label: 'Home', icon: 'home' },
  { href: '#services', label: 'Services', icon: 'services' },
  { href: '#about', label: 'About', icon: 'galaxy' },
  { href: '#contact', label: 'Contact', icon: 'satellite' },
]

// Footer links
export const footerLinks = [
  { href: '#home', label: 'Home', icon: 'home' },
  { href: '#services', label: 'Services', icon: 'services' },
  { href: '#about', label: 'About', icon: 'galaxy' },
  { href: '#contact', label: 'Contact', icon: 'satellite' },
]

// SEO and meta information
export const seoData = {
  title: 'Eclipse Softworks | Premier Software Development & AI Solutions',
  description: 'Leading software development company in South Africa. Custom software, web apps, mobile development, and AI solutions with a cosmic approach to innovation.',
  keywords: 'software development, AI solutions, web development, mobile apps, South Africa, custom software, machine learning, cloud infrastructure',
  ogImage: 'https://eclipse-softworks.com/og-image.jpg',
  twitterHandle: '@EclipseSoftworks', // Keep for legacy meta tags, but use Facebook for social links
  facebookHandle: 'eclipse.softworks'
}
