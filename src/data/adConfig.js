// Eclipse Softworks - Advertisement Configuration
// Update this file to manage your advertising settings

export const adConfig = {
  // Google AdSense Configuration
  googleAdsense: {
    enabled: false, // Set to true when you have AdSense approval
    publisherId: 'ca-pub-YOUR_PUBLISHER_ID', // Replace with your actual AdSense publisher ID
    adSlots: {
      banner: 'YOUR_BANNER_AD_SLOT_ID',
      sidebar: 'YOUR_SIDEBAR_AD_SLOT_ID',
      native: 'YOUR_NATIVE_AD_SLOT_ID',
      mobile: 'YOUR_MOBILE_AD_SLOT_ID'
    }
  },

  // Facebook Audience Network (since you're using Facebook)
  facebookAds: {
    enabled: false,
    placementId: 'YOUR_FACEBOOK_PLACEMENT_ID'
  },

  // Direct/Custom Ads
  directAds: {
    enabled: true, // You can use this for direct advertising deals
    ads: [
      {
        id: 'partner-1',
        title: 'Premium Hosting Solutions',
        description: 'Stellar cloud hosting for your cosmic projects',
        imageUrl: '/ads/hosting-ad.jpg',
        linkUrl: 'https://example-hosting.com',
        position: 'banner',
        active: true
      },
      {
        id: 'partner-2',
        title: 'AI Development Tools',
        description: 'Next-generation AI tools for developers',
        imageUrl: '/ads/ai-tools-ad.jpg',
        linkUrl: 'https://example-ai-tools.com',
        position: 'native',
        active: true
      }
    ]
  },

  // Ad Placement Settings
  placements: {
    showAfterHero: true,
    showBetweenSections: true,
    showInSidebar: false, // Set to true if you add a sidebar
    showInFooter: true,
    showOnMobile: true
  },

  // Ad Display Rules
  displayRules: {
    maxAdsPerPage: 3,
    minScrollPercentage: 25, // Show ads after user scrolls 25%
    respectDoNotTrack: true,
    showCloseButton: true
  },

  // Analytics and Tracking
  analytics: {
    trackClicks: true,
    trackImpressions: true,
    trackConversions: false
  }
}

// Custom ad content for your space theme
export const customAdContent = {
  placeholders: [
    {
      title: 'Stellar Partnership Opportunity',
      description: 'Advertise your cosmic solutions here',
      icon: 'rocket'
    },
    {
      title: 'Galactic Sponsorship',
      description: 'Reach developers across the digital universe',
      icon: 'galaxy'
    },
    {
      title: 'Space-Age Promotion',
      description: 'Launch your brand into the stratosphere',
      icon: 'satellite'
    }
  ]
}

// Revenue tracking (for your reference)
export const revenueTracking = {
  monthlyGoal: 1000, // USD
  currentMonth: 0,
  sources: {
    googleAdsense: 0,
    directAds: 0,
    affiliateMarketing: 0
  }
}

export default adConfig
