// Eclipse Softworks - Email Service Configuration
// This service handles contact form submissions using multiple providers

// EmailJS Configuration (Recommended - Free tier: 200 emails/month)
export const emailJSConfig = {
  serviceId: 'YOUR_EMAILJS_SERVICE_ID', // Replace with your EmailJS service ID
  templateId: 'YOUR_EMAILJS_TEMPLATE_ID', // Replace with your EmailJS template ID
  publicKey: 'YOUR_EMAILJS_PUBLIC_KEY', // Replace with your EmailJS public key
}

// FormSubmit Configuration (Backup option)
export const formSubmitConfig = {
  endpoint: 'https://formsubmit.co/<EMAIL>',
  settings: {
    _subject: 'New Contact Form Submission - Eclipse Softworks',
    _captcha: 'false',
    _template: 'table',
    _next: window.location.origin + '/thank-you', // Redirect after submission
  }
}

// Netlify Forms Configuration (If hosting on Netlify)
export const netlifyConfig = {
  formName: 'contact',
  honeypot: 'bot-field', // Anti-spam field
}

// Email validation utility
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Form validation utility
export const validateForm = (formData) => {
  const errors = {}
  
  if (!formData.name || formData.name.trim().length < 2) {
    errors.name = 'Name must be at least 2 characters long'
  }
  
  if (!formData.email || !validateEmail(formData.email)) {
    errors.email = 'Please enter a valid email address'
  }
  
  if (!formData.message || formData.message.trim().length < 10) {
    errors.message = 'Message must be at least 10 characters long'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// EmailJS submission function
export const sendEmailJS = async (formData) => {
  try {
    // Load EmailJS if not already loaded
    if (!window.emailjs) {
      const script = document.createElement('script')
      script.src = 'https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js'
      document.head.appendChild(script)
      
      await new Promise((resolve) => {
        script.onload = resolve
      })
      
      window.emailjs.init(emailJSConfig.publicKey)
    }
    
    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      message: formData.message,
      to_email: '<EMAIL>',
      reply_to: formData.email,
    }
    
    const response = await window.emailjs.send(
      emailJSConfig.serviceId,
      emailJSConfig.templateId,
      templateParams
    )
    
    return { success: true, response }
  } catch (error) {
    console.error('EmailJS error:', error)
    return { success: false, error: error.message }
  }
}

// FormSubmit submission function (backup)
export const sendFormSubmit = async (formData) => {
  try {
    const submitData = new FormData()
    submitData.append('name', formData.name)
    submitData.append('email', formData.email)
    submitData.append('message', formData.message)
    
    // Add FormSubmit configuration
    Object.entries(formSubmitConfig.settings).forEach(([key, value]) => {
      submitData.append(key, value)
    })
    
    const response = await fetch(formSubmitConfig.endpoint, {
      method: 'POST',
      body: submitData,
      headers: {
        'Accept': 'application/json'
      }
    })
    
    if (response.ok) {
      return { success: true, response }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  } catch (error) {
    console.error('FormSubmit error:', error)
    return { success: false, error: error.message }
  }
}

// Netlify Forms submission function
export const sendNetlifyForm = async (formData) => {
  try {
    const submitData = new FormData()
    submitData.append('form-name', netlifyConfig.formName)
    submitData.append('name', formData.name)
    submitData.append('email', formData.email)
    submitData.append('message', formData.message)
    
    const response = await fetch('/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams(submitData).toString()
    })
    
    if (response.ok) {
      return { success: true, response }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
  } catch (error) {
    console.error('Netlify Forms error:', error)
    return { success: false, error: error.message }
  }
}

// Main email service function with fallback options
export const sendEmail = async (formData) => {
  // Validate form data first
  const validation = validateForm(formData)
  if (!validation.isValid) {
    return { success: false, errors: validation.errors }
  }
  
  // Try EmailJS first (most reliable)
  if (emailJSConfig.serviceId !== 'YOUR_EMAILJS_SERVICE_ID') {
    const emailJSResult = await sendEmailJS(formData)
    if (emailJSResult.success) {
      return emailJSResult
    }
    console.warn('EmailJS failed, trying FormSubmit...')
  }
  
  // Fallback to FormSubmit
  const formSubmitResult = await sendFormSubmit(formData)
  if (formSubmitResult.success) {
    return formSubmitResult
  }
  
  // If both fail, return error
  return {
    success: false,
    error: 'All email services failed. Please try again later or contact us directly.'
  }
}

export default {
  sendEmail,
  validateForm,
  validateEmail,
  sendEmailJS,
  sendFormSubmit,
  sendNetlifyForm
}
