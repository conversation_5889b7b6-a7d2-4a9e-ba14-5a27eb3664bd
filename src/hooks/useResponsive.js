import { useState, useEffect } from 'react'

// Custom hook for responsive design
export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  })

  const [device, setDevice] = useState('desktop')

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setScreenSize({ width, height })
      
      // Determine device type
      if (width < 640) {
        setDevice('mobile')
      } else if (width < 1024) {
        setDevice('tablet')
      } else {
        setDevice('desktop')
      }
    }

    // Set initial values
    handleResize()

    // Add event listener
    window.addEventListener('resize', handleResize)

    // Cleanup
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return {
    ...screenSize,
    device,
    isMobile: device === 'mobile',
    isTablet: device === 'tablet',
    isDesktop: device === 'desktop',
    isSmallScreen: screenSize.width < 1024,
  }
}

// Hook for detecting touch devices
export const useTouch = () => {
  const [isTouch, setIsTouch] = useState(false)

  useEffect(() => {
    const checkTouch = () => {
      setIsTouch('ontouchstart' in window || navigator.maxTouchPoints > 0)
    }
    
    checkTouch()
  }, [])

  return isTouch
}

// Hook for reduced motion preference
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e) => setPrefersReducedMotion(e.matches)
    mediaQuery.addEventListener('change', handleChange)

    return () => mediaQuery.removeEventListener('change', handleChange)
  }, [])

  return prefersReducedMotion
}

// Hook for network speed detection
export const useNetworkSpeed = () => {
  const [connectionSpeed, setConnectionSpeed] = useState('fast')

  useEffect(() => {
    if ('connection' in navigator) {
      const connection = navigator.connection
      
      const updateConnectionSpeed = () => {
        const effectiveType = connection.effectiveType
        
        switch (effectiveType) {
          case 'slow-2g':
          case '2g':
            setConnectionSpeed('slow')
            break
          case '3g':
            setConnectionSpeed('medium')
            break
          case '4g':
          default:
            setConnectionSpeed('fast')
            break
        }
      }

      updateConnectionSpeed()
      connection.addEventListener('change', updateConnectionSpeed)

      return () => connection.removeEventListener('change', updateConnectionSpeed)
    }
  }, [])

  return connectionSpeed
}

export default {
  useResponsive,
  useTouch,
  useReducedMotion,
  useNetworkSpeed
}
