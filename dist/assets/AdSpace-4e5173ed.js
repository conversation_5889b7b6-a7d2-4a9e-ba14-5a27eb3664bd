import{j as e}from"./index-217e3814.js";import{r as a}from"./vendor-79b9f383.js";import{m as s}from"./animations-b0678ed1.js";const t=({size:t="banner",position:r="center",className:l="",adContent:o=null,placeholder:i=!0})=>{const[n,c]=a.useState(!0),d=()=>e.jsxs("div",{className:"relative w-full h-full bg-gradient-to-br from-space-dark/80 to-space-blue/20 rounded-xl border border-space-blue/30 overflow-hidden group",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-space-blue/10 to-transparent animate-pulse"}),e.jsx("div",{className:"absolute inset-0",children:[...Array(6)].map((a,t)=>e.jsx(s.div,{className:"absolute w-1 h-1 bg-space-cyan rounded-full",style:{left:20+12*t+"%",top:30+8*t+"%"},animate:{y:[0,-10,0],opacity:[.3,1,.3]},transition:{duration:2+.3*t,repeat:1/0,delay:.2*t}},t))}),e.jsx("div",{className:"relative z-10 flex flex-col items-center justify-center h-full p-4 text-center",children:e.jsxs(s.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.6},className:"space-y-2",children:[e.jsx("div",{className:"text-space-blue",children:e.jsx("svg",{className:"w-8 h-8 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),e.jsx("h4",{className:"text-sm font-orbitron font-bold text-space-cyan",children:"Stellar Advertisement Space"}),e.jsx("p",{className:"text-xs text-star-silver font-inter",children:"Your cosmic message here"})]})}),e.jsx("button",{onClick:()=>c(!1),className:"absolute top-2 right-2 w-6 h-6 bg-space-dark/80 hover:bg-space-blue/20 rounded-full flex items-center justify-center text-star-silver hover:text-space-cyan transition-colors duration-300 opacity-0 group-hover:opacity-100","aria-label":"Close ad",children:e.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),u=({adSlot:s,adFormat:t="auto"})=>(a.useEffect(()=>{try{(window.adsbygoogle=window.adsbygoogle||[]).push({})}catch(e){}},[]),e.jsx("ins",{className:"adsbygoogle",style:{display:"block"},"data-ad-client":"ca-pub-YOUR_PUBLISHER_ID","data-ad-slot":s,"data-ad-format":t,"data-full-width-responsive":"true"}));return n?e.jsx(s.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},viewport:{once:!0},className:`${{banner:"w-full h-24 md:h-32",square:"w-64 h-64",sidebar:"w-48 h-96",native:"w-full h-auto min-h-32",mobile:"w-full h-20"}[t]} ${{center:"mx-auto",left:"mr-auto",right:"ml-auto"}[r]} ${l}`,children:o?e.jsx("div",{className:"w-full h-full",children:o}):i?e.jsx(d,{}):e.jsx(u,{adSlot:"YOUR_AD_SLOT_ID"})}):null},r=a=>e.jsx(t,{size:"banner",...a}),l=a=>e.jsx(t,{size:"sidebar",...a}),o=a=>e.jsx(t,{size:"square",...a}),i=a=>e.jsx(t,{size:"native",...a});export{r as BannerAd,i as NativeAd,l as SidebarAd,o as SquareAd,t as default};
