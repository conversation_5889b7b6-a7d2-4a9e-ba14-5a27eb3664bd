import{j as e,c as a,I as t}from"./index-217e3814.js";import{m as s}from"./animations-b0678ed1.js";import"./vendor-79b9f383.js";const i=()=>e.jsxs("section",{id:"about",className:"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[e.jsxs("div",{className:"absolute inset-0",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-space-dark/90 via-space-dark/70 to-space-dark/90"}),e.jsx("div",{className:"absolute top-1/3 left-1/4 w-96 h-96 bg-space-purple/20 rounded-full blur-3xl animate-pulse"}),e.jsx("div",{className:"absolute bottom-1/4 right-1/3 w-80 h-80 bg-space-cyan/15 rounded-full blur-3xl animate-pulse",style:{animationDelay:"3s"}}),[...Array(20)].map((a,t)=>e.jsx("div",{className:"absolute w-1 h-1 bg-star-white rounded-full animate-twinkle",style:{left:100*Math.random()+"%",top:100*Math.random()+"%",animationDelay:5*Math.random()+"s"}},t))]}),e.jsx("div",{className:"max-w-6xl mx-auto relative z-10",children:e.jsxs(s.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-16",children:[e.jsx("div",{className:"text-center",children:e.jsxs(s.h2,{className:"text-4xl sm:text-5xl lg:text-6xl font-orbitron font-bold text-star-white mb-6",initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:1,delay:.2},viewport:{once:!0},children:["About ",e.jsx("span",{className:"bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent glow-text",children:"Eclipse Softworks"})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[e.jsxs("div",{className:"space-y-8",children:[e.jsxs(s.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"space-card",children:[e.jsx("h3",{className:"text-2xl font-orbitron font-bold text-space-blue mb-4",children:"Our Cosmic Mission"}),e.jsx("p",{className:"text-lg text-star-silver leading-relaxed font-inter",children:"Eclipse Softworks (Pty) Ltd is a South African software and AI innovation company dedicated to building next-generation tools and digital systems. We navigate the cosmos of technology to deliver solutions that transform businesses and drive meaningful impact across Africa and beyond."})]}),e.jsxs(s.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"space-card",children:[e.jsx("h3",{className:"text-2xl font-orbitron font-bold text-space-purple mb-4",children:"Innovating with Purpose"}),e.jsxs("p",{className:"text-lg text-star-silver leading-relaxed font-inter",children:["Our mission transcends earthly boundaries: ",e.jsx("strong",{className:"text-space-blue glow-text",children:"Innovating with Purpose"}),". We believe technology should solve real problems and create stellar opportunities. From AI-powered automation to custom enterprise constellations, we craft solutions that are not just technically excellent, but strategically aligned with your cosmic goals."]})]})]}),e.jsx(s.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"relative",children:e.jsxs("div",{className:"relative w-full h-96 rounded-2xl overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-space-blue/30 via-space-purple/20 to-space-cyan/30 backdrop-blur-sm"}),[...Array(3)].map((a,t)=>e.jsx(s.div,{className:"absolute border border-space-blue/30 rounded-full",style:{width:80*(t+1)+"px",height:80*(t+1)+"px",top:"50%",left:"50%",transform:"translate(-50%, -50%)"},animate:{rotate:360},transition:{duration:10+5*t,repeat:1/0,ease:"linear"}},t)),e.jsx("div",{className:"absolute top-1/2 left-1/2 w-8 h-8 -translate-x-1/2 -translate-y-1/2 bg-gradient-to-r from-space-blue to-space-purple rounded-full animate-pulse-glow"}),[...Array(8)].map((a,t)=>e.jsx(s.div,{className:"absolute w-2 h-2 bg-star-white rounded-full",style:{left:20+10*t+"%",top:30+5*t+"%"},animate:{y:[0,-20,0],opacity:[.3,1,.3]},transition:{duration:3+.5*t,repeat:1/0,delay:.2*t}},t))]})})]}),e.jsx(s.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.7},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:a.map((a,i)=>e.jsxs(s.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.8+.1*i},viewport:{once:!0},whileHover:{scale:1.05,y:-5},className:"text-center space-card",children:[e.jsx("div",{className:"text-space-blue mb-2",children:e.jsx(t,{name:a.icon,size:"2xl"})}),e.jsx("div",{className:"text-3xl font-orbitron font-bold bg-gradient-to-r from-space-blue to-space-purple bg-clip-text text-transparent mb-2",children:a.number}),e.jsx("div",{className:"text-star-silver font-inter text-sm",children:a.label})]},a.label))})]})})]});export{i as default};
