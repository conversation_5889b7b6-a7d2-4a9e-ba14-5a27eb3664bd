var e=Object.defineProperty,t=(t,n,i)=>(((t,n,i)=>{n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[n]=i})(t,"symbol"!=typeof n?n+"":n,i),i);let n=-1;const i=e=>{addEventListener("pageshow",t=>{t.persisted&&(n=t.timeStamp,e(t))},!0)},r=(e,t,n,i)=>{let r,s;return o=>{var a,l;t.value>=0&&(o||i)&&(s=t.value-(r??0),(s||void 0===r)&&(r=t.value,t.delta=s,t.rating=(a=t.value)>(l=n)[1]?"poor":a>l[0]?"needs-improvement":"good",e(t)))}},s=e=>{requestAnimationFrame(()=>requestAnimationFrame(()=>e()))},o=()=>{const e=performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},a=()=>{const e=o();return(null==e?void 0:e.activationStart)??0},l=(e,t=-1)=>{const i=o();let r="navigate";return n>=0?r="back-forward-cache":i&&(document.prerendering||a()>0?r="prerender":document.wasDiscarded?r="restore":i.type&&(r=i.type.replace(/_/g,"-"))),{name:e,value:t,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function d(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class h{constructor(){t(this,"t"),t(this,"i",0),t(this,"o",[])}h(e){var t;if(e.hadRecentInput)return;const n=this.o[0],i=this.o.at(-1);this.i&&n&&i&&e.startTime-i.startTime<1e3&&e.startTime-n.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),null==(t=this.t)||t.call(this,e)}}const u=(e,t,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const i=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return i.observe({type:e,buffered:!0,...n}),i}}catch{}},m=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let p=-1;const v=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,g=e=>{"hidden"===document.visibilityState&&p>-1&&(p="visibilitychange"===e.type?e.timeStamp:0,T())},f=()=>{addEventListener("visibilitychange",g,!0),addEventListener("prerenderingchange",g,!0)},T=()=>{removeEventListener("visibilitychange",g,!0),removeEventListener("prerenderingchange",g,!0)},y=()=>{var e;if(p<0){const t=a(),n=document.prerendering||null==(e=globalThis.performance.getEntriesByType("visibility-state").filter(e=>"hidden"===e.name&&e.startTime>t)[0])?void 0:e.startTime;p=n??v(),f(),i(()=>{setTimeout(()=>{p=v(),f()})})}return{get firstHiddenTime(){return p}}},b=e=>{document.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},C=[1800,3e3],L=(e,t={})=>{b(()=>{const n=y();let o,c=l("FCP");const d=u("paint",e=>{for(const t of e)"first-contentful-paint"===t.name&&(d.disconnect(),t.startTime<n.firstHiddenTime&&(c.value=Math.max(t.startTime-a(),0),c.entries.push(t),o(!0)))});d&&(o=r(e,c,C,t.reportAllChanges),i(n=>{c=l("FCP"),o=r(e,c,C,t.reportAllChanges),s(()=>{c.value=performance.now()-n.timeStamp,o(!0)})}))})},P=[.1,.25],E=(e,t={})=>{L(m(()=>{let n,o=l("CLS",0);const a=d(t,h),c=e=>{for(const t of e)a.h(t);a.i>o.value&&(o.value=a.i,o.entries=a.o,n())},m=u("layout-shift",c);m&&(n=r(e,o,P,t.reportAllChanges),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(c(m.takeRecords()),n(!0))}),i(()=>{a.i=0,o=l("CLS",0),n=r(e,o,P,t.reportAllChanges),s(()=>n())}),setTimeout(n))}))};let S=0,w=1/0,A=0;const I=e=>{for(const t of e)t.interactionId&&(w=Math.min(w,t.interactionId),A=Math.max(A,t.interactionId),S=A?(A-w)/7+1:0)};let M;const F=()=>M?S:performance.interactionCount??0;let k=0;class B{constructor(){t(this,"u",[]),t(this,"l",new Map),t(this,"m"),t(this,"v")}p(){k=F(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((F()-k)/50));return this.u[e]}h(e){var t,n;if(null==(t=this.m)||t.call(this,e),!e.interactionId&&"first-input"!==e.entryType)return;const i=this.u.at(-1);let r=this.l.get(e.interactionId);if(r||this.u.length<10||e.duration>i.T){if(r?e.duration>r.T?(r.entries=[e],r.T=e.duration):e.duration===r.T&&e.startTime===r.entries[0].startTime&&r.entries.push(e):(r={id:e.interactionId,entries:[e],T:e.duration},this.l.set(r.id,r),this.u.push(r)),this.u.sort((e,t)=>t.T-e.T),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}null==(n=this.v)||n.call(this,r)}}}const x=e=>{const t=globalThis.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?e():(e=m(e),document.addEventListener("visibilitychange",e,{once:!0}),t(()=>{e(),document.removeEventListener("visibilitychange",e)}))},N=[200,500],R=(e,t={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&b(()=>{"interactionCount"in performance||M||(M=u("event",I,{type:"event",buffered:!0,durationThreshold:0}));let n,s=l("INP");const o=d(t,B),a=e=>{x(()=>{for(const n of e)o.h(n);const t=o.P();t&&t.T!==s.value&&(s.value=t.T,s.entries=t.entries,n())})},c=u("event",a,{durationThreshold:t.durationThreshold??40});n=r(e,s,N,t.reportAllChanges),c&&(c.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(a(c.takeRecords()),n(!0))}),i(()=>{o.p(),s=l("INP"),n=r(e,s,N,t.reportAllChanges)}))})};class q{constructor(){t(this,"m")}h(e){var t;null==(t=this.m)||t.call(this,e)}}const H=[2500,4e3],O=(e,t={})=>{b(()=>{const n=y();let o,c=l("LCP");const h=d(t,q),p=e=>{t.reportAllChanges||(e=e.slice(-1));for(const t of e)h.h(t),t.startTime<n.firstHiddenTime&&(c.value=Math.max(t.startTime-a(),0),c.entries=[t],o())},v=u("largest-contentful-paint",p);if(v){o=r(e,c,H,t.reportAllChanges);const n=m(()=>{p(v.takeRecords()),v.disconnect(),o(!0)});for(const e of["keydown","click","visibilitychange"])addEventListener(e,()=>x(n),{capture:!0,once:!0});i(n=>{c=l("LCP"),o=r(e,c,H,t.reportAllChanges),s(()=>{c.value=performance.now()-n.timeStamp,o(!0)})})}})},D=[800,1800],$=e=>{document.prerendering?b(()=>$(e)):"complete"!==document.readyState?addEventListener("load",()=>$(e),!0):setTimeout(e)},j=(e,t={})=>{let n=l("TTFB"),s=r(e,n,D,t.reportAllChanges);$(()=>{const c=o();c&&(n.value=Math.max(c.responseStart-a(),0),n.entries=[c],s(!0),i(()=>{n=l("TTFB",0),s=r(e,n,D,t.reportAllChanges),s(!0)}))})};export{P as CLSThresholds,C as FCPThresholds,N as INPThresholds,H as LCPThresholds,D as TTFBThresholds,E as onCLS,L as onFCP,R as onINP,O as onLCP,j as onTTFB};
