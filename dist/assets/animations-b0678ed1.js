import{r as t,R as e}from"./vendor-79b9f383.js";const n=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),i=t.createContext({}),s=t.createContext(null),o="undefined"!=typeof document,r=o?t.useLayoutEffect:t.useEffect,a=t.createContext({strict:!1}),l=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),u="data-"+l("framerAppearId");function c(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function h(t){return"string"==typeof t||Array.isArray(t)}function d(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}const p=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],m=["initial",...p];function f(t){return d(t.animate)||m.some(e=>h(t[e]))}function g(t){return Boolean(f(t)||t.variants)}function y(e){const{initial:n,animate:s}=function(t,e){if(f(t)){const{initial:e,animate:n}=t;return{initial:!1===e||h(e)?e:void 0,animate:h(n)?n:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(i));return t.useMemo(()=>({initial:n,animate:s}),[v(n),v(s)])}function v(t){return Array.isArray(t)?t.join(" "):t}const x={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},P={};for(const Zo in x)P[Zo]={isEnabled:t=>x[Zo].some(e=>!!t[e])};const T=t.createContext({}),b=t.createContext({}),w=Symbol.for("motionComponentSymbol");function S({preloadedFeatures:e,createVisualElement:l,useRender:h,useVisualState:d,Component:p}){e&&function(t){for(const e in t)P[e]={...P[e],...t[e]}}(e);const m=t.forwardRef(function(m,f){let g;const v={...t.useContext(n),...m,layoutId:A(m)},{isStatic:x}=v,P=y(m),T=d(m,x);if(!x&&o){P.visualElement=function(e,o,l,c){const{visualElement:h}=t.useContext(i),d=t.useContext(a),p=t.useContext(s),m=t.useContext(n).reducedMotion,f=t.useRef();c=c||d.renderer,!f.current&&c&&(f.current=c(e,{visualState:o,parent:h,props:l,presenceContext:p,blockInitialAnimation:!!p&&!1===p.initial,reducedMotionConfig:m}));const g=f.current;t.useInsertionEffect(()=>{g&&g.update(l,p)});const y=t.useRef(Boolean(l[u]&&!window.HandoffComplete));return r(()=>{g&&(g.render(),y.current&&g.animationState&&g.animationState.animateChanges())}),t.useEffect(()=>{g&&(g.updateFeatures(),!y.current&&g.animationState&&g.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),g}(p,T,v,l);const o=t.useContext(b),c=t.useContext(a).strict;P.visualElement&&(g=P.visualElement.loadFeatures(v,c,e,o))}return t.createElement(i.Provider,{value:P},g&&P.visualElement?t.createElement(g,{visualElement:P.visualElement,...v}):null,h(p,m,function(e,n,i){return t.useCallback(t=>{t&&e.mount&&e.mount(t),n&&(t?n.mount(t):n.unmount()),i&&("function"==typeof i?i(t):c(i)&&(i.current=t))},[n])}(T,P.visualElement,f),T,x,P.visualElement))});return m[w]=p,m}function A({layoutId:e}){const n=t.useContext(T).id;return n&&void 0!==e?n+"-"+e:e}function V(t){function e(e,n={}){return S(t(e,n))}if("undefined"==typeof Proxy)return e;const n=new Map;return new Proxy(e,{get:(t,i)=>(n.has(i)||n.set(i,e(i)),n.get(i))})}const E=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function C(t){return"string"==typeof t&&!t.includes("-")&&!!(E.indexOf(t)>-1||/[A-Z]/.test(t))}const M={};const D=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],k=new Set(D);function R(t,{layout:e,layoutId:n}){return k.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!M[t]||"opacity"===t)}const L=t=>Boolean(t&&t.getVelocity),j={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},B=D.length;const F=t=>e=>"string"==typeof e&&e.startsWith(t),O=F("--"),I=F("var(--"),U=(t,e)=>e&&"number"==typeof t?e.transform(t):t,N=(t,e,n)=>Math.min(Math.max(n,t),e),W={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},$={...W,transform:t=>N(0,1,t)},H={...W,default:1},z=t=>Math.round(1e5*t)/1e5,Y=/(-)?([\d]*\.?[\d])+/g,X=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,G=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function q(t){return"string"==typeof t}const Z=t=>({test:e=>q(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),K=Z("deg"),_=Z("%"),J=Z("px"),Q=Z("vh"),tt=Z("vw"),et={..._,parse:t=>_.parse(t)/100,transform:t=>_.transform(100*t)},nt={...W,transform:Math.round},it={borderWidth:J,borderTopWidth:J,borderRightWidth:J,borderBottomWidth:J,borderLeftWidth:J,borderRadius:J,radius:J,borderTopLeftRadius:J,borderTopRightRadius:J,borderBottomRightRadius:J,borderBottomLeftRadius:J,width:J,maxWidth:J,height:J,maxHeight:J,size:J,top:J,right:J,bottom:J,left:J,padding:J,paddingTop:J,paddingRight:J,paddingBottom:J,paddingLeft:J,margin:J,marginTop:J,marginRight:J,marginBottom:J,marginLeft:J,rotate:K,rotateX:K,rotateY:K,rotateZ:K,scale:H,scaleX:H,scaleY:H,scaleZ:H,skew:K,skewX:K,skewY:K,distance:J,translateX:J,translateY:J,translateZ:J,x:J,y:J,z:J,perspective:J,transformPerspective:J,opacity:$,originX:et,originY:et,originZ:J,zIndex:nt,fillOpacity:$,strokeOpacity:$,numOctaves:nt};function st(t,e,n,i){const{style:s,vars:o,transform:r,transformOrigin:a}=t;let l=!1,u=!1,c=!0;for(const h in e){const t=e[h];if(O(h)){o[h]=t;continue}const n=it[h],i=U(t,n);if(k.has(h)){if(l=!0,r[h]=i,!c)continue;t!==(n.default||0)&&(c=!1)}else h.startsWith("origin")?(u=!0,a[h]=i):s[h]=i}if(e.transform||(l||i?s.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:n=!0},i,s){let o="";for(let r=0;r<B;r++){const e=D[r];void 0!==t[e]&&(o+=`${j[e]||e}(${t[e]}) `)}return e&&!t.z&&(o+="translateZ(0)"),o=o.trim(),s?o=s(t,i?"":o):n&&i&&(o="none"),o}(t.transform,n,c,i):s.transform&&(s.transform="none")),u){const{originX:t="50%",originY:e="50%",originZ:n=0}=a;s.transformOrigin=`${t} ${e} ${n}`}}const ot=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rt(t,e,n){for(const i in e)L(e[i])||R(i,n)||(t[i]=e[i])}function at(e,n,i){const s={};return rt(s,e.style||{},e),Object.assign(s,function({transformTemplate:e},n,i){return t.useMemo(()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return st(t,n,{enableHardwareAcceleration:!i},e),Object.assign({},t.vars,t.style)},[n])}(e,n,i)),e.transformValues?e.transformValues(s):s}function lt(t,e,n){const i={},s=at(t,e,n);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i}const ut=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ct(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ut.has(t)}let ht=t=>!ct(t);try{(dt=require("@emotion/is-prop-valid").default)&&(ht=t=>t.startsWith("on")?!ct(t):dt(t))}catch(qo){}var dt;function pt(t,e,n){return"string"==typeof t?t:J.transform(e+n*t)}const mt={offset:"stroke-dashoffset",array:"stroke-dasharray"},ft={offset:"strokeDashoffset",array:"strokeDasharray"};function gt(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h,d){if(st(t,u,c,d),h)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==s||void 0!==o||m.transform)&&(m.transformOrigin=function(t,e,n){return`${pt(e,t.x,t.width)} ${pt(n,t.y,t.height)}`}(f,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(p.x=e),void 0!==n&&(p.y=n),void 0!==i&&(p.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?mt:ft;t[o.offset]=J.transform(-i);const r=J.transform(e),a=J.transform(n);t[o.array]=`${r} ${a}`}(p,r,a,l,!1)}const yt=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),vt=t=>"string"==typeof t&&"svg"===t.toLowerCase();function xt(e,n,i,s){const o=t.useMemo(()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return gt(t,n,{enableHardwareAcceleration:!1},vt(s),e.transformTemplate),{...t.attrs,style:{...t.style}}},[n]);if(e.style){const t={};rt(t,e.style,e),o.style={...t,...o.style}}return o}function Pt(e=!1){return(n,i,s,{latestValues:o},r)=>{const a=(C(n)?xt:lt)(i,o,r,n),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(ht(s)||!0===n&&ct(s)||!e&&!ct(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(i,"string"==typeof n,e),u={...l,...a,ref:s},{children:c}=i,h=t.useMemo(()=>L(c)?c.get():c,[c]);return t.createElement(n,{...u,children:h})}}function Tt(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}const bt=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function wt(t,e,n,i){Tt(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(bt.has(s)?s:l(s),e.attrs[s])}function St(t,e){const{style:n}=t,i={};for(const s in n)(L(n[s])||e.style&&L(e.style[s])||R(s,t))&&(i[s]=n[s]);return i}function At(t,e){const n=St(t,e);for(const i in t)if(L(t[i])||L(e[i])){n[-1!==D.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]}return n}function Vt(t,e,n,i={},s={}){return"function"==typeof e&&(e=e(void 0!==n?n:t.custom,i,s)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==n?n:t.custom,i,s)),e}const Et=t=>Array.isArray(t),Ct=t=>Et(t)?t[t.length-1]||0:t;function Mt(t){const e=L(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const Dt=e=>(n,o)=>{const r=t.useContext(i),a=t.useContext(s),l=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:n},i,s,o){const r={latestValues:kt(i,s,o,t),renderState:e()};return n&&(r.mount=t=>n(i,t,r)),r}(e,n,r,a);return o?l():function(e){const n=t.useRef(null);return null===n.current&&(n.current=e()),n.current}(l)};function kt(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=Mt(o[d]);let{initial:r,animate:a}=t;const l=f(t),u=g(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!d(h)){(Array.isArray(h)?h:[h]).forEach(e=>{const n=Vt(t,e);if(!n)return;const{transitionEnd:i,transition:o,...r}=n;for(const t in r){let e=r[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const t in i)s[t]=i[t]})}return s}const Rt=t=>t;class Lt{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}const jt=["prepare","read","update","preRender","render","postRender"];const{schedule:Bt,cancel:Ft,state:Ot,steps:It}=function(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=jt.reduce((t,e)=>(t[e]=function(t){let e=new Lt,n=new Lt,i=0,s=!1,o=!1;const r=new WeakSet,a={schedule:(t,o=!1,a=!1)=>{const l=a&&s,u=l?e:n;return o&&r.add(t),u.add(t)&&l&&s&&(i=e.order.length),t},cancel:t=>{n.remove(t),r.delete(t)},process:l=>{if(s)o=!0;else{if(s=!0,[e,n]=[n,e],n.clear(),i=e.order.length,i)for(let n=0;n<i;n++){const i=e.order[n];i(l),r.has(i)&&(a.schedule(i),t())}s=!1,o&&(o=!1,a.process(l))}}};return a}(()=>n=!0),t),{}),r=t=>o[t].process(s),a=()=>{const o=performance.now();n=!1,s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1),s.timestamp=o,s.isProcessing=!0,jt.forEach(r),s.isProcessing=!1,n&&e&&(i=!1,t(a))};return{schedule:jt.reduce((e,r)=>{const l=o[r];return e[r]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(a)),l.schedule(e,o,r)),e},{}),cancel:t=>jt.forEach(e=>o[e].cancel(t)),state:s,steps:o}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:Rt,!0),Ut={useVisualState:Dt({scrapeMotionValuesFromProps:At,createRenderState:yt,onMount:(t,e,{renderState:n,latestValues:i})=>{Bt.read(()=>{try{n.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){n.dimensions={x:0,y:0,width:0,height:0}}}),Bt.render(()=>{gt(n,i,{enableHardwareAcceleration:!1},vt(e.tagName),t.transformTemplate),wt(e,n)})}})},Nt={useVisualState:Dt({scrapeMotionValuesFromProps:St,createRenderState:ot})};function Wt(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const $t=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function Ht(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}function zt(t,e,n,i){return Wt(t,e,(t=>e=>$t(e)&&t(e,Ht(e)))(n),i)}const Yt=(t,e)=>n=>e(t(n)),Xt=(...t)=>t.reduce(Yt);function Gt(t){let e=null;return()=>{const n=()=>{e=null};return null===e&&(e=t,n)}}const qt=Gt("dragHorizontal"),Zt=Gt("dragVertical");function Kt(t){let e=!1;if("y"===t)e=Zt();else if("x"===t)e=qt();else{const t=qt(),n=Zt();t&&n?e=()=>{t(),n()}:(t&&t(),n&&n())}return e}function _t(){const t=Kt(!0);return!t||(t(),!1)}class Jt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Qt(t,e){const n="pointer"+(e?"enter":"leave"),i="onHover"+(e?"Start":"End");return zt(t.current,n,(n,s)=>{if("touch"===n.pointerType||_t())return;const o=t.getProps();t.animationState&&o.whileHover&&t.animationState.setActive("whileHover",e),o[i]&&Bt.update(()=>o[i](n,s))},{passive:!t.getProps()[i]})}const te=(t,e)=>!!e&&(t===e||te(t,e.parentElement));function ee(t,e){if(!e)return;const n=new PointerEvent("pointer"+t);e(n,Ht(n))}const ne=new WeakMap,ie=new WeakMap,se=t=>{const e=ne.get(t.target);e&&e(t)},oe=t=>{t.forEach(se)};function re(t,e,n){const i=function({root:t,...e}){const n=t||document;ie.has(n)||ie.set(n,{});const i=ie.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(oe,{root:t,...e})),i[s]}(e);return ne.set(t,n),i.observe(t),()=>{ne.delete(t),i.unobserve(t)}}const ae={some:0,all:1};const le={inView:{Feature:class extends Jt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:ae[i]};return re(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Jt{constructor(){super(...arguments),this.removeStartListeners=Rt,this.removeEndListeners=Rt,this.removeAccessibleListeners=Rt,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),i=zt(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:i,globalTapTarget:s}=this.node.getProps();Bt.update(()=>{s||te(this.node.current,t.target)?n&&n(t,e):i&&i(t,e)})},{passive:!(n.onTap||n.onPointerUp)}),s=zt(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=Xt(i,s),this.startPress(t,e)},this.startAccessiblePress=()=>{const t=Wt(this.node.current,"keydown",t=>{if("Enter"!==t.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=Wt(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&ee("up",(t,e)=>{const{onTap:n}=this.node.getProps();n&&Bt.update(()=>n(t,e))})}),ee("down",(t,e)=>{this.startPress(t,e)})}),e=Wt(this.node.current,"blur",()=>{this.isPressing&&ee("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=Xt(t,e)}}startPress(t,e){this.isPressing=!0;const{onTapStart:n,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&Bt.update(()=>n(t,e))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!_t()}cancelPress(t,e){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&Bt.update(()=>n(t,e))}mount(){const t=this.node.getProps(),e=zt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),n=Wt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Xt(e,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends Jt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Xt(Wt(this.node.current,"focus",()=>this.onFocus()),Wt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Jt{mount(){this.unmount=Xt(Qt(this.node,!0),Qt(this.node,!1))}unmount(){}}}};function ue(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function ce(t,e,n){const i=t.getProps();return Vt(i,e,void 0!==n?n:i.custom,function(t){const e={};return t.values.forEach((t,n)=>e[n]=t.get()),e}(t),function(t){const e={};return t.values.forEach((t,n)=>e[n]=t.getVelocity()),e}(t))}let he=Rt,de=Rt;const pe=t=>1e3*t,me=t=>t/1e3,fe=!1,ge=t=>Array.isArray(t)&&"number"==typeof t[0];function ye(t){return Boolean(!t||"string"==typeof t&&xe[t]||ge(t)||Array.isArray(t)&&t.every(ye))}const ve=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,xe={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ve([0,.65,.55,1]),circOut:ve([.55,0,1,.45]),backIn:ve([.31,.01,.66,-.59]),backOut:ve([.33,1.53,.69,.99])};function Pe(t){if(t)return ge(t)?ve(t):Array.isArray(t)?t.map(Pe):xe[t]}const Te=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function be(t,e,n,i){if(t===e&&n===i)return Rt;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=Te(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:Te(s(t),e,i)}const we=be(.42,0,1,1),Se=be(0,0,.58,1),Ae=be(.42,0,.58,1),Ve=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ee=t=>e=>1-t(1-e),Ce=t=>1-Math.sin(Math.acos(t)),Me=Ee(Ce),De=Ve(Ce),ke=be(.33,1.53,.69,.99),Re=Ee(ke),Le=Ve(Re),je={linear:Rt,easeIn:we,easeInOut:Ae,easeOut:Se,circIn:Ce,circInOut:De,circOut:Me,backIn:Re,backInOut:Le,backOut:ke,anticipate:t=>(t*=2)<1?.5*Re(t):.5*(2-Math.pow(2,-10*(t-1)))},Be=t=>{if(Array.isArray(t)){de(4===t.length);const[e,n,i,s]=t;return be(e,n,i,s)}return"string"==typeof t?je[t]:t},Fe=(t,e)=>n=>Boolean(q(n)&&G.test(n)&&n.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(n,e)),Oe=(t,e,n)=>i=>{if(!q(i))return i;const[s,o,r,a]=i.match(Y);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},Ie={...W,transform:t=>Math.round((t=>N(0,255,t))(t))},Ue={test:Fe("rgb","red"),parse:Oe("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+Ie.transform(t)+", "+Ie.transform(e)+", "+Ie.transform(n)+", "+z($.transform(i))+")"};const Ne={test:Fe("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:Ue.transform},We={test:Fe("hsl","hue"),parse:Oe("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+_.transform(z(e))+", "+_.transform(z(n))+", "+z($.transform(i))+")"},$e={test:t=>Ue.test(t)||Ne.test(t)||We.test(t),parse:t=>Ue.test(t)?Ue.parse(t):We.test(t)?We.parse(t):Ne.parse(t),transform:t=>q(t)?t:t.hasOwnProperty("red")?Ue.transform(t):We.transform(t)},He=(t,e,n)=>-n*t+n*e+t;function ze(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}const Ye=(t,e,n)=>{const i=t*t;return Math.sqrt(Math.max(0,n*(e*e-i)+i))},Xe=[Ne,Ue,We];function Ge(t){const e=(n=t,Xe.find(t=>t.test(n)));var n;let i=e.parse(t);return e===We&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=ze(a,i,t+1/3),o=ze(a,i,t),r=ze(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const qe=(t,e)=>{const n=Ge(t),i=Ge(e),s={...n};return t=>(s.red=Ye(n.red,i.red,t),s.green=Ye(n.green,i.green,t),s.blue=Ye(n.blue,i.blue,t),s.alpha=He(n.alpha,i.alpha,t),Ue.transform(s))};const Ze={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:Rt},Ke={regex:X,countKey:"Colors",token:"${c}",parse:$e.parse},_e={regex:Y,countKey:"Numbers",token:"${n}",parse:W.parse};function Je(t,{regex:e,countKey:n,token:i,parse:s}){const o=t.tokenised.match(e);o&&(t["num"+n]=o.length,t.tokenised=t.tokenised.replace(e,i),t.values.push(...o.map(s)))}function Qe(t){const e=t.toString(),n={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Je(n,Ze),Je(n,Ke),Je(n,_e),n}function tn(t){return Qe(t).values}function en(t){const{values:e,numColors:n,numVars:i,tokenised:s}=Qe(t),o=e.length;return t=>{let e=s;for(let s=0;s<o;s++)e=s<i?e.replace(Ze.token,t[s]):s<i+n?e.replace(Ke.token,$e.transform(t[s])):e.replace(_e.token,z(t[s]));return e}}const nn=t=>"number"==typeof t?0:t;const sn={test:function(t){var e,n;return isNaN(t)&&q(t)&&((null===(e=t.match(Y))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(X))||void 0===n?void 0:n.length)||0)>0},parse:tn,createTransformer:en,getAnimatableNone:function(t){const e=tn(t);return en(t)(e.map(nn))}},on=(t,e)=>n=>`${n>0?e:t}`;function rn(t,e){return"number"==typeof t?n=>He(t,e,n):$e.test(t)?qe(t,e):t.startsWith("var(")?on(t,e):un(t,e)}const an=(t,e)=>{const n=[...t],i=n.length,s=t.map((t,n)=>rn(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}},ln=(t,e)=>{const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=rn(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}},un=(t,e)=>{const n=sn.createTransformer(e),i=Qe(t),s=Qe(e);return i.numVars===s.numVars&&i.numColors===s.numColors&&i.numNumbers>=s.numNumbers?Xt(an(i.values,s.values),n):on(t,e)},cn=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i},hn=(t,e)=>n=>He(t,e,n);function dn(t,e,n){const i=[],s=n||("number"==typeof(o=t[0])?hn:"string"==typeof o?$e.test(o)?qe:un:Array.isArray(o)?an:"object"==typeof o?ln:hn);var o;const r=t.length-1;for(let a=0;a<r;a++){let n=s(t[a],t[a+1]);if(e){const t=Array.isArray(e)?e[a]||Rt:e;n=Xt(t,n)}i.push(n)}return i}function pn(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(de(o===e.length),1===o)return()=>e[0];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const r=dn(e,i,s),a=r.length,l=e=>{let n=0;if(a>1)for(;n<t.length-2&&!(e<t[n+1]);n++);const i=cn(t[n],t[n+1],e);return r[n](i)};return n?e=>l(N(t[0],t[o-1],e)):l}function mn(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=cn(0,e,i);t.push(He(n,1,s))}}(e,t.length-1),e}function fn({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(Be):Be(i),o={done:!1,value:e[0]},r=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:mn(e),t),a=pn(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map(()=>u||Ae).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}function gn(t,e){return e?t*(1e3/e):0}function yn(t,e,n){const i=Math.max(e-5,0);return gn(n-t(i),e-i)}const vn=.001;function xn({duration:t=800,bounce:e=.25,velocity:n=0,mass:i=1}){let s,o;he(t<=pe(10));let r=1-e;r=N(.05,1,r),t=N(.01,10,me(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=Tn(e,r),l=Math.exp(-s);return vn-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=Tn(Math.pow(e,2),r);return(-s(e)+vn>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let s=1;s<Pn;s++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=pe(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const Pn=12;function Tn(t,e){return t*Math.sqrt(1-e*e)}const bn=["duration","bounce"],wn=["stiffness","damping","mass"];function Sn(t,e){return e.some(e=>void 0!==t[e])}function An({keyframes:t,restDelta:e,restSpeed:n,...i}){const s=t[0],o=t[t.length-1],r={done:!1,value:s},{stiffness:a,damping:l,mass:u,duration:c,velocity:h,isResolvedFromDuration:d}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!Sn(t,wn)&&Sn(t,bn)){const n=xn(t);e={...e,...n,mass:1},e.isResolvedFromDuration=!0}return e}({...i,velocity:-me(i.velocity||0)}),p=h||0,m=l/(2*Math.sqrt(a*u)),f=o-s,g=me(Math.sqrt(a/u)),y=Math.abs(f)<5;let v;if(n||(n=y?.01:2),e||(e=y?.005:.5),m<1){const t=Tn(g,m);v=e=>{const n=Math.exp(-m*g*e);return o-n*((p+m*g*f)/t*Math.sin(t*e)+f*Math.cos(t*e))}}else if(1===m)v=t=>o-Math.exp(-g*t)*(f+(p+g*f)*t);else{const t=g*Math.sqrt(m*m-1);v=e=>{const n=Math.exp(-m*g*e),i=Math.min(t*e,300);return o-n*((p+m*g*f)*Math.sinh(i)+t*f*Math.cosh(i))/t}}return{calculatedDuration:d&&c||null,next:t=>{const i=v(t);if(d)r.done=t>=c;else{let s=p;0!==t&&(s=m<1?yn(v,t,i):0);const a=Math.abs(s)<=n,l=Math.abs(o-i)<=e;r.done=a&&l}return r.value=r.done?o:i,r}}}function Vn({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let P,T;const b=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(P=t,T=An({keyframes:[d.value,p(d.value)],velocity:yn(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==P||(e=!0,x(t),b(t)),void 0!==P&&t>P?T.next(t-P):(!e&&x(t),d)}}}const En=t=>{const e=({timestamp:e})=>t(e);return{start:()=>Bt.update(e,!0),stop:()=>Ft(e),now:()=>Ot.isProcessing?Ot.timestamp:performance.now()}};function Cn(t){let e=0;let n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}const Mn={decay:Vn,inertia:Vn,tween:fn,keyframes:fn,spring:An};function Dn({autoplay:t=!0,delay:e=0,driver:n=En,keyframes:i,type:s="keyframes",repeat:o=0,repeatDelay:r=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:h,...d}){let p,m,f=1,g=!1;const y=()=>{m=new Promise(t=>{p=t})};let v;y();const x=Mn[s]||fn;let P;x!==fn&&"number"!=typeof i[0]&&(P=pn([0,100],i,{clamp:!1}),i=[0,100]);const T=x({...d,keyframes:i});let b;"mirror"===a&&(b=x({...d,keyframes:[...i].reverse(),velocity:-(d.velocity||0)}));let w="idle",S=null,A=null,V=null;null===T.calculatedDuration&&o&&(T.calculatedDuration=Cn(T));const{calculatedDuration:E}=T;let C=1/0,M=1/0;null!==E&&(C=E+r,M=C*(o+1)-r);let D=0;const k=t=>{if(null===A)return;f>0&&(A=Math.min(A,t)),f<0&&(A=Math.min(t-M/f,A)),D=null!==S?S:Math.round(t-A)*f;const n=D-e*(f>=0?1:-1),s=f>=0?n<0:n>M;D=Math.max(n,0),"finished"===w&&null===S&&(D=M);let l=D,u=T;if(o){const t=Math.min(D,M)/C;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,o+1);Boolean(e%2)&&("reverse"===a?(n=1-n,r&&(n-=r/C)):"mirror"===a&&(u=b)),l=N(0,1,n)*C}const c=s?{done:!1,value:i[0]}:u.next(l);P&&(c.value=P(c.value));let{done:d}=c;s||null===E||(d=f>=0?D>=M:D<=0);const p=null===S&&("finished"===w||"running"===w&&d);return h&&h(c.value),p&&j(),c},R=()=>{v&&v.stop(),v=void 0},L=()=>{w="idle",R(),p(),y(),A=V=null},j=()=>{w="finished",c&&c(),R(),p()},B=()=>{if(g)return;v||(v=n(k));const t=v.now();l&&l(),null!==S?A=t-S:A&&"finished"!==w||(A=t),"finished"===w&&y(),V=A,S=null,w="running",v.start()};t&&B();const F={then:(t,e)=>m.then(t,e),get time(){return me(D)},set time(t){t=pe(t),D=t,null===S&&v&&0!==f?A=v.now()-t/f:S=t},get duration(){const t=null===T.calculatedDuration?Cn(T):T.calculatedDuration;return me(t)},get speed(){return f},set speed(t){t!==f&&v&&(f=t,F.time=me(D))},get state(){return w},play:B,pause:()=>{w="paused",S=D},stop:()=>{g=!0,"idle"!==w&&(w="idle",u&&u(),L())},cancel:()=>{null!==V&&k(V),L()},complete:()=>{w="finished"},sample:t=>(A=0,k(t))};return F}const kn=function(t){let e;return()=>(void 0===e&&(e=t()),e)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Rn=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function Ln(t,e,{onUpdate:n,onComplete:i,...s}){if(!(kn()&&Rn.has(e)&&!s.repeatDelay&&"mirror"!==s.repeatType&&0!==s.damping&&"inertia"!==s.type))return!1;let o,r,a=!1,l=!1;const u=()=>{r=new Promise(t=>{o=t})};u();let{keyframes:c,duration:h=300,ease:d,times:p}=s;if(((t,e)=>"spring"===e.type||"backgroundColor"===t||!ye(e.ease))(e,s)){const t=Dn({...s,repeat:0,delay:0});let e={done:!1,value:c[0]};const n=[];let i=0;for(;!e.done&&i<2e4;)e=t.sample(i),n.push(e.value),i+=10;p=void 0,c=n,h=i-10,d="linear"}const m=function(t,e,n,{delay:i=0,duration:s,repeat:o=0,repeatType:r="loop",ease:a,times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=Pe(a);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}(t.owner.current,e,c,{...s,duration:h,ease:d,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,Bt.update(f),o(),u()};m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:n="loop"}){return t[e&&"loop"!==n&&e%2==1?0:t.length-1]}(c,s)),i&&i(),g())};return{then:(t,e)=>r.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,Rt),get time(){return me(m.currentTime||0)},set time(t){m.currentTime=pe(t)},get speed(){return m.playbackRate},set speed(t){m.playbackRate=t},get duration(){return me(h)},play:()=>{a||(m.play(),Ft(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;const{currentTime:e}=m;if(e){const n=Dn({...s,autoplay:!1});t.setWithVelocity(n.sample(e-10).value,n.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}const jn={type:"spring",stiffness:500,damping:25,restSpeed:10},Bn={type:"keyframes",duration:.8},Fn={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},On=(t,{keyframes:e})=>e.length>2?Bn:k.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:jn:Fn,In=(t,e)=>"zIndex"!==t&&(!("number"!=typeof e&&!Array.isArray(e))||!("string"!=typeof e||!sn.test(e)&&"0"!==e||e.startsWith("url("))),Un=new Set(["brightness","contrast","saturate","opacity"]);function Nn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(Y)||[];if(!i)return t;const s=n.replace(i,"");let o=Un.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Wn=/([a-z-]*)\(.*?\)/g,$n={...sn,getAnimatableNone:t=>{const e=t.match(Wn);return e?e.map(Nn).join(" "):t}},Hn={...it,color:$e,backgroundColor:$e,outlineColor:$e,fill:$e,stroke:$e,borderColor:$e,borderTopColor:$e,borderRightColor:$e,borderBottomColor:$e,borderLeftColor:$e,filter:$n,WebkitFilter:$n},zn=t=>Hn[t];function Yn(t,e){let n=zn(t);return n!==$n&&(n=sn),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Xn=t=>/^0[^.\s]+$/.test(t);function Gn(t){return"number"==typeof t?0===t:null!==t?"none"===t||"0"===t||Xn(t):void 0}function qn(t,e){return t[e]||t.default||t}const Zn=!1,Kn=(t,e,n,i={})=>s=>{const o=qn(i,t)||{},r=o.delay||i.delay||0;let{elapsed:a=0}=i;a-=pe(r);const l=function(t,e,n,i){const s=In(e,n);let o;o=Array.isArray(n)?[...n]:[null,n];const r=void 0!==i.from?i.from:t.get();let a;const l=[];for(let u=0;u<o.length;u++)null===o[u]&&(o[u]=0===u?r:o[u-1]),Gn(o[u])&&l.push(u),"string"==typeof o[u]&&"none"!==o[u]&&"0"!==o[u]&&(a=o[u]);if(s&&l.length&&a)for(let u=0;u<l.length;u++)o[l[u]]=Yn(e,a);return o}(e,t,n,o),u=l[0],c=l[l.length-1],h=In(t,u),d=In(t,c);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{s(),o.onComplete&&o.onComplete()}};if(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)||(p={...p,...On(t,p)}),p.duration&&(p.duration=pe(p.duration)),p.repeatDelay&&(p.repeatDelay=pe(p.repeatDelay)),!h||!d||fe||!1===o.type||Zn)return function({keyframes:t,delay:e,onUpdate:n,onComplete:i}){const s=()=>(n&&n(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:Rt,pause:Rt,stop:Rt,then:t=>(t(),Promise.resolve()),cancel:Rt,complete:Rt});return e?Dn({keyframes:[0,1],duration:0,delay:e,onComplete:s}):s()}(p);if(!i.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){const n=Ln(e,t,p);if(n)return n}return Dn(p)};function _n(t){return Boolean(L(t)&&t.add)}const Jn=t=>/^\-?\d*\.?\d+$/.test(t);function Qn(t,e){-1===t.indexOf(e)&&t.push(e)}function ti(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class ei{constructor(){this.subscriptions=[]}add(t){return Qn(this.subscriptions,t),()=>ti(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}class ni{constructor(t,e={}){var n;this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;const{delta:n,timestamp:i}=Ot;this.lastUpdated!==i&&(this.timeDelta=n,this.lastUpdated=i,Bt.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>Bt.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=(n=this.current,!isNaN(parseFloat(n))),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ei);const n=this.events[t].add(e);return"change"===t?()=>{n(),Bt.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=t,this.timeDelta=n}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?gn(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ii(t,e){return new ni(t,e)}const si=t=>e=>e.test(t),oi=[W,J,_,K,tt,Q,{test:t=>"auto"===t,parse:t=>t}],ri=t=>oi.find(si(t)),ai=[...oi,$e,sn],li=t=>ai.find(si(t));function ui(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ii(n))}function ci(t,e){if(!e)return;return(e[t]||e.default||e).from}function hi({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function di(t,e){const n=t.get();if(!Array.isArray(e))return n!==e;for(let i=0;i<e.length;i++)if(e[i]!==n)return!0}function pi(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=t.makeTargetAnimatable(e);const l=t.getValue("willChange");i&&(o=i);const c=[],h=s&&t.animationState&&t.animationState.getState()[s];for(const d in a){const e=t.getValue(d),i=a[d];if(!e||void 0===i||h&&hi(h,d))continue;const s={delay:n,elapsed:0,...qn(o||{},d)};if(window.HandoffAppearAnimations){const n=t.getProps()[u];if(n){const t=window.HandoffAppearAnimations(n,d,e,Bt);null!==t&&(s.elapsed=t,s.isHandoff=!0)}}let r=!s.isHandoff&&!di(e,i);if("spring"===s.type&&(e.getVelocity()||s.velocity)&&(r=!1),e.animation&&(r=!1),r)continue;e.start(Kn(d,e,i,t.shouldReduceMotion&&k.has(d)?{type:!1}:s));const p=e.animation;_n(l)&&(l.add(d),p.then(()=>l.remove(d))),c.push(p)}return r&&Promise.all(c).then(()=>{r&&function(t,e){const n=ce(t,e);let{transitionEnd:i={},transition:s={},...o}=n?t.makeTargetAnimatable(n,!1):{};o={...o,...i};for(const r in o)ui(t,r,Ct(o[r]))}(t,r)}),c}function mi(t,e,n={}){const i=ce(t,e,n.custom);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(pi(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(fi).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(mi(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,o+i,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(n.delay)])}function fi(t,e){return t.sortNodePosition(e)}const gi=[...p].reverse(),yi=p.length;function vi(t){return e=>Promise.all(e.map(({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>mi(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=mi(t,e,n);else{const s="function"==typeof e?ce(t,e,n.custom):e;i=Promise.all(pi(t,s,n))}return i.then(()=>t.notify("AnimationComplete",e))}(t,e,n)))}function xi(t){let e=vi(t);const n={animate:Ti(!0),whileInView:Ti(),whileHover:Ti(),whileTap:Ti(),whileDrag:Ti(),whileFocus:Ti(),exit:Ti()};let i=!0;const s=(e,n)=>{const i=ce(t,n);if(i){const{transition:t,transitionEnd:n,...s}=i;e={...e,...s,...n}}return e};function o(o,r){const a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],c=new Set;let p={},m=1/0;for(let e=0;e<yi;e++){const f=gi[e],g=n[f],y=void 0!==a[f]?a[f]:l[f],v=h(y),x=f===r?g.isActive:null;!1===x&&(m=e);let P=y===l[f]&&y!==a[f]&&v;if(P&&i&&t.manuallyAnimateOnMount&&(P=!1),g.protectedKeys={...p},!g.isActive&&null===x||!y&&!g.prevProp||d(y)||"boolean"==typeof y)continue;let T=Pi(g.prevProp,y)||f===r&&g.isActive&&!P&&v||e>m&&v,b=!1;const w=Array.isArray(y)?y:[y];let S=w.reduce(s,{});!1===x&&(S={});const{prevResolvedValues:A={}}=g,V={...A,...S},E=t=>{T=!0,c.has(t)&&(b=!0,c.delete(t)),g.needsAnimating[t]=!0};for(const t in V){const e=S[t],n=A[t];if(p.hasOwnProperty(t))continue;let i=!1;i=Et(e)&&Et(n)?!ue(e,n):e!==n,i?void 0!==e?E(t):c.add(t):void 0!==e&&c.has(t)?E(t):g.protectedKeys[t]=!0}g.prevProp=y,g.prevResolvedValues=S,g.isActive&&(p={...p,...S}),i&&t.blockInitialAnimation&&(T=!1),!T||P&&!b||u.push(...w.map(t=>({animation:t,options:{type:f,...o}})))}if(c.size){const e={};c.forEach(n=>{const i=t.getBaseTarget(n);void 0!==i&&(e[n]=i)}),u.push({animation:e})}let f=Boolean(u.length);return!i||!1!==a.initial&&a.initial!==a.animate||t.manuallyAnimateOnMount||(f=!1),i=!1,f?e(u):Promise.resolve()}return{animateChanges:o,setActive:function(e,i,s){var r;if(n[e].isActive===i)return Promise.resolve();null===(r=t.variantChildren)||void 0===r||r.forEach(t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)}),n[e].isActive=i;const a=o(s,e);for(const t in n)n[t].protectedKeys={};return a},setAnimateFunction:function(n){e=n(t)},getState:()=>n}}function Pi(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!ue(e,t)}function Ti(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let bi=0;const wi={animation:{Feature:class extends Jt{constructor(t){super(t),t.animationState||(t.animationState=xi(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),d(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends Jt{constructor(){super(...arguments),this.id=bi++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e,custom:n}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t,{custom:null!=n?n:this.node.getProps().custom});e&&!t&&s.then(()=>e(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}}},Si=(t,e)=>Math.abs(t-e);class Ai{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Ci(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=Si(t.x,e.x),i=Si(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=Ot;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Vi(e,this.transformPagePoint),Bt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Ci("pointercancel"===t.type?this.lastMoveEventInfo:Vi(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!$t(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=Vi(Ht(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=Ot;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Ci(o,this.history)),this.removeListeners=Xt(zt(this.contextWindow,"pointermove",this.handlePointerMove),zt(this.contextWindow,"pointerup",this.handlePointerUp),zt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Ft(this.updatePoint)}}function Vi(t,e){return e?{point:e(t.point)}:t}function Ei(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Ci({point:t},e){return{point:t,delta:Ei(t,Di(e)),offset:Ei(t,Mi(e)),velocity:ki(e,.1)}}function Mi(t){return t[0]}function Di(t){return t[t.length-1]}function ki(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=Di(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>pe(e)));)n--;if(!i)return{x:0,y:0};const o=me(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Ri(t){return t.max-t.min}function Li(t,e=0,n=.01){return Math.abs(t-e)<=n}function ji(t,e,n,i=.5){t.origin=i,t.originPoint=He(e.min,e.max,t.origin),t.scale=Ri(n)/Ri(e),(Li(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=He(n.min,n.max,t.origin)-t.originPoint,(Li(t.translate)||isNaN(t.translate))&&(t.translate=0)}function Bi(t,e,n,i){ji(t.x,e.x,n.x,i?i.originX:void 0),ji(t.y,e.y,n.y,i?i.originY:void 0)}function Fi(t,e,n){t.min=n.min+e.min,t.max=t.min+Ri(e)}function Oi(t,e,n){t.min=e.min-n.min,t.max=t.min+Ri(e)}function Ii(t,e,n){Oi(t.x,e.x,n.x),Oi(t.y,e.y,n.y)}function Ui(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Ni(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Wi=.35;function $i(t,e,n){return{min:Hi(t,e),max:Hi(t,n)}}function Hi(t,e){return"number"==typeof t?t:t[e]||0}const zi=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Yi(t){return[t("x"),t("y")]}function Xi({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Gi(t){return void 0===t||1===t}function qi({scale:t,scaleX:e,scaleY:n}){return!Gi(t)||!Gi(e)||!Gi(n)}function Zi(t){return qi(t)||Ki(t)||t.z||t.rotate||t.rotateX||t.rotateY}function Ki(t){return _i(t.x)||_i(t.y)}function _i(t){return t&&"0%"!==t}function Ji(t,e,n){return n+e*(t-n)}function Qi(t,e,n,i,s){return void 0!==s&&(t=Ji(t,s,i)),Ji(t,n,i)+e}function ts(t,e=0,n=1,i,s){t.min=Qi(t.min,e,n,i,s),t.max=Qi(t.max,e,n,i,s)}function es(t,{x:e,y:n}){ts(t.x,e.translate,e.scale,e.originPoint),ts(t.y,n.translate,n.scale,n.originPoint)}function ns(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function is(t,e){t.min=t.min+e,t.max=t.max+e}function ss(t,e,[n,i,s]){const o=void 0!==e[s]?e[s]:.5,r=He(t.min,t.max,o);ts(t,e[n],e[i],r,e.scale)}const os=["x","scaleX","originX"],rs=["y","scaleY","originY"];function as(t,e){ss(t.x,e,os),ss(t.y,e,rs)}function ls(t,e){return Xi(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const us=({current:t})=>t?t.ownerDocument.defaultView:null,cs=new WeakMap;class hs{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Ai(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(Ht(t,"page").point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Kt(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Yi(t=>{let e=this.getAxisMotionValue(t).get()||0;if(_.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Ri(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&Bt.update(()=>s(t,e),!1,!0);const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Yi(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:us(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&Bt.update(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!ds(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?He(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?He(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&c(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Ui(t.x,n,s),y:Ui(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=Wi){return!1===t?t=0:!0===t&&(t=Wi),{x:$i(t,"left","right"),y:$i(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Yi(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!c(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=ls(t,n),{scroll:s}=e;return s&&(is(i.x,s.offset.x),is(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Ni(t.x,e.x),y:Ni(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Xi(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Yi(r=>{if(!ds(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return n.start(Kn(t,n,0,e))}stopAnimation(){Yi(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Yi(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e="_drag"+t.toUpperCase(),n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Yi(e=>{const{drag:n}=this.getProps();if(!ds(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-He(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!c(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Yi(t=>{const e=this.getAxisMotionValue(t);if(e){const n=e.get();i[t]=function(t,e){let n=.5;const i=Ri(t),s=Ri(e);return s>i?n=cn(e.min,e.max-i,t.min):i>s&&(n=cn(t.min,t.max-s,e.min)),N(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Yi(e=>{if(!ds(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(He(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;cs.set(this.visualElement,this);const t=zt(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();c(t)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),e();const s=Wt(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Yi(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Wi,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function ds(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const ps=t=>(e,n)=>{t&&Bt.update(()=>t(e,n))};const ms={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function fs(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const gs={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!J.test(t))return t;t=parseFloat(t)}return`${fs(t,e.target.x)}% ${fs(t,e.target.y)}%`}},ys={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=sn.parse(t);if(s.length>5)return i;const o=sn.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=He(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class vs extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;var o;o=Ps,Object.assign(M,o),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),ms.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||Bt.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function xs(n){const[i,o]=function(){const e=t.useContext(s);if(null===e)return[!0,null];const{isPresent:n,onExitComplete:i,register:o}=e,r=t.useId();return t.useEffect(()=>o(r),[]),!n&&i?[!1,()=>i&&i(r)]:[!0]}(),r=t.useContext(T);return e.createElement(vs,{...n,layoutGroup:r,switchLayoutGroup:t.useContext(b),isPresent:i,safeToRemove:o})}const Ps={borderRadius:{...gs,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:gs,borderTopRightRadius:gs,borderBottomLeftRadius:gs,borderBottomRightRadius:gs,boxShadow:ys},Ts=["TopLeft","TopRight","BottomLeft","BottomRight"],bs=Ts.length,ws=t=>"string"==typeof t?parseFloat(t):t,Ss=t=>"number"==typeof t||J.test(t);function As(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Vs=Cs(0,.5,Me),Es=Cs(.5,.95,Rt);function Cs(t,e,n){return i=>i<t?0:i>e?1:n(cn(t,e,i))}function Ms(t,e){t.min=e.min,t.max=e.max}function Ds(t,e){Ms(t.x,e.x),Ms(t.y,e.y)}function ks(t,e,n,i,s){return t=Ji(t-=e,1/n,i),void 0!==s&&(t=Ji(t,1/s,i)),t}function Rs(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){_.test(e)&&(e=parseFloat(e),e=He(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=He(o.min,o.max,i);t===o&&(a-=e),t.min=ks(t.min,e,n,a,s),t.max=ks(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Ls=["x","scaleX","originX"],js=["y","scaleY","originY"];function Bs(t,e,n,i){Rs(t.x,e,Ls,n?n.x:void 0,i?i.x:void 0),Rs(t.y,e,js,n?n.y:void 0,i?i.y:void 0)}function Fs(t){return 0===t.translate&&1===t.scale}function Os(t){return Fs(t.x)&&Fs(t.y)}function Is(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function Us(t){return Ri(t.x)/Ri(t.y)}class Ns{constructor(){this.members=[]}add(t){Qn(this.members,t),t.scheduleRender()}remove(t){if(ti(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Ws(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y;if((s||o)&&(i=`translate3d(${s}px, ${o}px, 0) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{rotate:t,rotateX:e,rotateY:s}=n;t&&(i+=`rotate(${t}deg) `),e&&(i+=`rotateX(${e}deg) `),s&&(i+=`rotateY(${s}deg) `)}const r=t.x.scale*e.x,a=t.y.scale*e.y;return 1===r&&1===a||(i+=`scale(${r}, ${a})`),i||"none"}const $s=(t,e)=>t.depth-e.depth;class Hs{constructor(){this.children=[],this.isDirty=!1}add(t){Qn(this.children,t),this.isDirty=!0}remove(t){ti(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort($s),this.isDirty=!1,this.children.forEach(t)}}const zs=["","X","Y","Z"],Ys={visibility:"hidden"};let Xs=0;const Gs={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function qs({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=Xs++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{var t;this.projectionUpdateScheduled=!1,Gs.totalNodes=Gs.resolvedTargetDeltas=Gs.recalculatedProjection=0,this.nodes.forEach(_s),this.nodes.forEach(so),this.nodes.forEach(oo),this.nodes.forEach(Js),t=Gs,window.MotionDebug&&window.MotionDebug.record(t)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Hs)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ei),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;var i;this.isSVG=(i=e)instanceof SVGElement&&"svg"!==i.tagName,this.instance=e;const{layoutId:s,layout:o,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||s)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=performance.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(Ft(i),t(o-e))};return Bt.read(i,!0),()=>Ft(i)}(i,250),ms.hasAnimatedSinceResize&&(ms.hasAnimatedSinceResize=!1,this.nodes.forEach(io))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||o)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||r.getDefaultTransition()||ho,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!Is(this.targetLayout,i)||n,u=!e&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...qn(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||io(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Ft(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ro),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(to);this.isUpdating||this.nodes.forEach(eo),this.isUpdating=!1,this.nodes.forEach(no),this.nodes.forEach(Zs),this.nodes.forEach(Ks),this.clearAllSnapshots();const t=performance.now();Ot.delta=N(0,1e3/60,t-Ot.timestamp),Ot.timestamp=t,Ot.isProcessing=!0,It.update.process(Ot),It.preRender.process(Ot),It.render.process(Ot),Ot.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Qs),this.sharedNodes.forEach(ao)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Bt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Bt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:n(this.instance)})}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!Os(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||Zi(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),fo((i=n).x),fo(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox(),{scroll:n}=this.root;return n&&(is(e.x,n.offset.x),is(e.y,n.offset.y)),e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Ds(e,t);for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;if(i!==this.root&&s&&o.layoutScroll){if(s.isRoot){Ds(e,t);const{scroll:n}=this.root;n&&(is(e.x,-n.offset.x),is(e.y,-n.offset.y))}is(e.x,s.offset.x),is(e.y,s.offset.y)}}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Ds(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&as(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),Zi(t.latestValues)&&as(n,t.latestValues)}return Zi(this.latestValues)&&as(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Ds(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!Zi(t.latestValues))continue;qi(t.latestValues)&&t.updateSnapshot();const i=zi();Ds(i,t.measurePageBox()),Bs(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return Zi(this.latestValues)&&Bs(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Ot.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=Ot.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ii(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Ds(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var r,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,Fi(r.x,a.x,l.x),Fi(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Ds(this.target,this.layout.layoutBox),es(this.target,this.targetDelta)):Ds(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Ii(this.relativeTargetOrigin,this.target,t.target),Ds(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Gs.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!qi(this.parent.latestValues)&&!Ki(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===Ot.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;Ds(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const s=o.instance;s&&s.style&&"contents"===s.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&as(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,es(t,r)),i&&Zi(o.latestValues)&&as(t,o.latestValues))}e.x=ns(e.x),e.y=ns(e.y)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox);const{target:l}=e;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;Bi(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=Ws(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===r&&this.treeScale.y===a||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Gs.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(co));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,g;lo(o.x,t.x,n),lo(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Ii(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,g=n,uo(p.x,m.x,f.x,g),uo(p.y,m.y,f.y,g),h&&(l=this.relativeTarget,d=h,l.x.min===d.x.min&&l.x.max===d.x.max&&l.y.min===d.y.min&&l.y.max===d.y.max)&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),Ds(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=He(0,void 0!==n.opacity?n.opacity:1,Vs(i)),t.opacityExit=He(void 0!==e.opacity?e.opacity:1,0,Es(i))):o&&(t.opacity=He(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let r=0;r<bs;r++){const s=`border${Ts[r]}Radius`;let o=As(e,s),a=As(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||Ss(o)===Ss(a)?(t[s]=Math.max(He(ws(o),ws(a),i),0),(_.test(a)||_.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=He(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Ft(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Bt.update(()=>{ms.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,n){const i=L(t)?t:ii(t);return i.start(Kn("",i,e,n)),i.animation}(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&go(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Ri(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Ri(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Ds(e,n),as(e,s),Bi(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new Ns);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(e=!0),!e)return;const i={};for(let s=0;s<zs.length;s++){const e="rotate"+zs[s];n[e]&&(i[e]=n[e],t.setStaticValue(e,0))}t.render();for(const s in i)t.setStaticValue(s,i[s]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Ys;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=Mt(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Mt(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!Zi(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=Ws(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const u in M){if(void 0===r[u])continue;const{correct:t,applyTo:e}=M[u],n="none"===i.transform?r[u]:t(r[u],o);if(e){const t=e.length;for(let s=0;s<t;s++)i[e[s]]=n}else i[u]=n}return this.options.layoutId&&(i.pointerEvents=o===this?Mt(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(to),this.root.sharedNodes.clear()}}}function Zs(t){t.updateLayout()}function Ks(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?Yi(t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Ri(i);i.min=e[t].min,i.max=i.min+s}):go(s,n.layoutBox,e)&&Yi(i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Ri(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Bi(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Bi(a,t.applyTransform(i,!0),n.measuredBox):Bi(a,e,n.layoutBox);const l=!Os(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Ii(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Ii(a,e,o.layoutBox),Is(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function _s(t){Gs.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Js(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Qs(t){t.clearSnapshot()}function to(t){t.clearMeasurements()}function eo(t){t.isLayoutDirty=!1}function no(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function io(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function so(t){t.resolveTargetDelta()}function oo(t){t.calcProjection()}function ro(t){t.resetRotation()}function ao(t){t.removeLeadSnapshot()}function lo(t,e,n){t.translate=He(e.translate,0,n),t.scale=He(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function uo(t,e,n,i){t.min=He(e.min,n.min,i),t.max=He(e.max,n.max,i)}function co(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const ho={duration:.45,ease:[.4,0,.1,1]},po=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),mo=po("applewebkit/")&&!po("chrome/")?Math.round:Rt;function fo(t){t.min=mo(t.min),t.max=mo(t.max)}function go(t,e,n){return"position"===t||"preserve-aspect"===t&&!Li(Us(e),Us(n),.2)}const yo=qs({attachResizeListener:(t,e)=>Wt(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),vo={current:void 0},xo=qs({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!vo.current){const t=new yo({});t.mount(window),t.setOptions({layoutScroll:!0}),vo.current=t}return vo.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Po={pan:{Feature:class extends Jt{constructor(){super(...arguments),this.removePointerDownListener=Rt}onPointerDown(t){this.session=new Ai(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:us(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:ps(t),onStart:ps(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&Bt.update(()=>i(t,e))}}}mount(){this.removePointerDownListener=zt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Jt{constructor(t){super(t),this.removeGroupControls=Rt,this.removeListeners=Rt,this.controls=new hs(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Rt}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:xo,MeasureLayout:xs}},To=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function bo(t,e,n=1){const[i,s]=function(t){const e=To.exec(t);if(!e)return[,];const[,n,i]=e;return[n,i]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return Jn(t)?parseFloat(t):t}return I(s)?bo(s,e,n+1):s}const wo=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),So=t=>wo.has(t),Ao=t=>t===W||t===J,Vo=(t,e)=>parseFloat(t.split(", ")[e]),Eo=(t,e)=>(n,{transform:i})=>{if("none"===i||!i)return 0;const s=i.match(/^matrix3d\((.+)\)$/);if(s)return Vo(s[1],e);{const e=i.match(/^matrix\((.+)\)$/);return e?Vo(e[1],t):0}},Co=new Set(["x","y","z"]),Mo=D.filter(t=>!Co.has(t));const Do={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Eo(4,13),y:Eo(5,14)};Do.translateX=Do.x,Do.translateY=Do.y;const ko=(t,e,n={},i={})=>{e={...e},i={...i};const s=Object.keys(e).filter(So);let r=[],a=!1;const l=[];if(s.forEach(s=>{const o=t.getValue(s);if(!t.hasValue(s))return;let u=n[s],c=ri(u);const h=e[s];let d;if(Et(h)){const t=h.length,e=null===h[0]?1:0;u=h[e],c=ri(u);for(let n=e;n<t&&null!==h[n];n++)d?de(ri(h[n])===d):d=ri(h[n])}else d=ri(h);if(c!==d)if(Ao(c)&&Ao(d)){const t=o.get();"string"==typeof t&&o.set(parseFloat(t)),"string"==typeof h?e[s]=parseFloat(h):Array.isArray(h)&&d===J&&(e[s]=h.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==d?void 0:d.transform)&&(0===u||0===h)?0===u?o.set(d.transform(u)):e[s]=c.transform(h):(a||(r=function(t){const e=[];return Mo.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),a=!0),l.push(s),i[s]=void 0!==i[s]?i[s]:e[s],o.jump(h))}),l.length){const n=l.indexOf("height")>=0?window.pageYOffset:null,s=((t,e,n)=>{const i=e.measureViewportBox(),s=e.current,o=getComputedStyle(s),{display:r}=o,a={};"none"===r&&e.setStaticValue("display",t.display||"block"),n.forEach(t=>{a[t]=Do[t](i,o)}),e.render();const l=e.measureViewportBox();return n.forEach(n=>{const i=e.getValue(n);i&&i.jump(a[n]),t[n]=Do[n](l,o)}),t})(e,t,l);return r.length&&r.forEach(([e,n])=>{t.getValue(e).set(n)}),t.render(),o&&null!==n&&window.scrollTo({top:n}),{target:s,transitionEnd:i}}return{target:e,transitionEnd:i}};function Ro(t,e,n,i){return(t=>Object.keys(t).some(So))(e)?ko(t,e,n,i):{target:e,transitionEnd:i}}const Lo=(t,e,n,i)=>{const s=function(t,{...e},n){const i=t.current;if(!(i instanceof Element))return{target:e,transitionEnd:n};n&&(n={...n}),t.values.forEach(t=>{const e=t.get();if(!I(e))return;const n=bo(e,i);n&&t.set(n)});for(const s in e){const t=e[s];if(!I(t))continue;const o=bo(t,i);o&&(e[s]=o,n||(n={}),void 0===n[s]&&(n[s]=t))}return{target:e,transitionEnd:n}}(t,e,i);return Ro(t,e=s.target,n,i=s.transitionEnd)},jo={current:null},Bo={current:!1};const Fo=new WeakMap,Oo=Object.keys(P),Io=Oo.length,Uo=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],No=m.length;class Wo{constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>Bt.render(this.render,!1,!0);const{latestValues:r,renderState:a}=s;this.latestValues=r,this.baseTarget={...r},this.initialValues=e.initial?{...r}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=f(e),this.isVariantNode=g(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(const c in u){const t=u[c];void 0!==r[c]&&L(t)&&(t.set(r[c],!1),_n(l)&&l.add(c))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,Fo.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Bo.current||function(){if(Bo.current=!0,o)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>jo.current=t.matches;t.addListener(e),e()}else jo.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||jo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Fo.delete(this.current),this.projection&&this.projection.unmount(),Ft(this.notifyUpdate),Ft(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){const n=k.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&Bt.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),s()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},n,i,s){let o,r;for(let a=0;a<Io;a++){const t=Oo[a],{isEnabled:n,Feature:i,ProjectionNode:s,MeasureLayout:l}=P[t];s&&(o=s),n(e)&&(!this.features[t]&&i&&(this.features[t]=new i(this)),l&&(r=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:t,layout:n,drag:i,dragConstraints:r,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:n,alwaysMeasureLayout:Boolean(i)||r&&c(r),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof n?n:"both",initialPromotionConfig:s,layoutScroll:a,layoutRoot:l})}return r}updateFeatures(){for(const t in this.features){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<Uo.length;n++){const e=Uo[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){const{willChange:i}=e;for(const s in e){const o=e[s],r=n[s];if(L(o))t.addValue(s,o),_n(i)&&i.add(s);else if(L(r))t.addValue(s,ii(o,{owner:t})),_n(i)&&i.remove(s);else if(r!==o)if(t.hasValue(s)){const e=t.getValue(s);!e.hasAnimated&&e.set(o)}else{const e=t.getStaticValue(s);t.addValue(s,ii(void 0!==e?e:o,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}const e={};for(let n=0;n<No;n++){const t=m[n],i=this.props[t];(h(i)||!1===i)&&(e[t]=i)}return e}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=ii(e,{owner:this}),this.addValue(t,n)),n}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props,i="string"==typeof n||"object"==typeof n?null===(e=Vt(this.props,n))||void 0===e?void 0:e[t]:void 0;if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||L(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new ei),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class $o extends Wo{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...n},{transformValues:i},s){let o=function(t,e,n){const i={};for(const s in t){const t=ci(s,e);if(void 0!==t)i[s]=t;else{const t=n.getValue(s);t&&(i[s]=t.get())}}return i}(n,t||{},this);if(i&&(e&&(e=i(e)),n&&(n=i(n)),o&&(o=i(o))),s){!function(t,e,n){var i,s;const o=Object.keys(e).filter(e=>!t.hasValue(e)),r=o.length;if(r)for(let a=0;a<r;a++){const r=o[a],l=e[r];let u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(s=null!==(i=n[r])&&void 0!==i?i:t.readValue(r))&&void 0!==s?s:e[r]),null!=u&&("string"==typeof u&&(Jn(u)||Xn(u))?u=parseFloat(u):!li(u)&&sn.test(l)&&(u=Yn(r,l)),t.addValue(r,ii(u,{owner:t})),void 0===n[r]&&(n[r]=u),null!==u&&t.setBaseTarget(r,u))}}(this,n,o);const t=Lo(this,n,o,e);e=t.transitionEnd,n=t.target}return{transition:t,transitionEnd:e,...n}}}class Ho extends $o{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(k.has(e)){const t=zn(e);return t&&t.default||0}{const i=(n=t,window.getComputedStyle(n)),s=(O(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return ls(t,e)}build(t,e,n,i){st(t,e,n,i.transformTemplate)}scrapeMotionValuesFromProps(t,e){return St(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;L(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,n,i){Tt(t,e,n,i)}}class zo extends $o{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(k.has(e)){const t=zn(e);return t&&t.default||0}return e=bt.has(e)?e:l(e),t.getAttribute(e)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(t,e){return At(t,e)}build(t,e,n,i){gt(t,e,n,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,n,i){wt(t,e,0,i)}mount(t){this.isSVGTag=vt(t.tagName),super.mount(t)}}const Yo=(t,e)=>C(t)?new zo(e,{enableHardwareAcceleration:!1}):new Ho(e,{enableHardwareAcceleration:!0}),Xo={...wi,...le,...Po,...{layout:{ProjectionNode:xo,MeasureLayout:xs}}},Go=V((t,e)=>function(t,{forwardMotionProps:e=!1},n,i){return{...C(t)?Ut:Nt,preloadedFeatures:n,useRender:Pt(e),createVisualElement:i,Component:t}}(t,e,Xo,Yo));export{Go as m};
