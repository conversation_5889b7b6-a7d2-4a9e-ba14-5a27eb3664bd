// Vercel Serverless Function for Contact Form with reCAPTCHA Verification
// This handles both CAPTCHA verification and email sending

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed. Use POST.' 
    });
  }

  try {
    const { name, email, message, captchaToken } = req.body;

    // Validate required fields
    if (!name || !email || !message || !captchaToken) {
      return res.status(400).json({ 
        success: false, 
        error: 'All fields including CAPTCHA verification are required' 
      });
    }

    // Verify reCAPTCHA first
    const captchaVerified = await verifyRecaptcha(captchaToken);
    
    if (!captchaVerified) {
      return res.status(400).json({ 
        success: false, 
        error: 'reCAPTCHA verification failed. Please try again.' 
      });
    }

    // Send email using FormSubmit or your preferred email service
    const emailSent = await sendEmail({ name, email, message });
    
    if (emailSent) {
      return res.status(200).json({ 
        success: true, 
        message: 'Message sent successfully! We\'ll respond within 24 hours.' 
      });
    } else {
      return res.status(500).json({ 
        success: false, 
        error: 'Failed to send email. Please try again.' 
      });
    }

  } catch (error) {
    console.error('Contact form error:', error);
    
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error. Please try again later.' 
    });
  }
}

// reCAPTCHA verification function
async function verifyRecaptcha(token) {
  try {
    const secretKey = process.env.RECAPTCHA_SECRET_KEY;
    
    if (!secretKey) {
      console.error('RECAPTCHA_SECRET_KEY environment variable not set');
      return false;
    }

    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${secretKey}&response=${token}`
    });

    const data = await response.json();
    
    console.log('reCAPTCHA verification:', {
      success: data.success,
      hostname: data.hostname,
      errorCodes: data['error-codes']
    });

    return data.success === true;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return false;
  }
}

// Email sending function using FormSubmit
async function sendEmail({ name, email, message }) {
  try {
    // Option 1: Use FormSubmit (recommended for simplicity)
    const formData = new FormData();
    formData.append('name', name);
    formData.append('email', email);
    formData.append('message', message);
    formData.append('_subject', 'New Contact Form Submission - Eclipse Softworks');
    formData.append('_captcha', 'false'); // We handle our own CAPTCHA
    formData.append('_template', 'table');

    const response = await fetch('https://formsubmit.co/<EMAIL>', {
      method: 'POST',
      body: formData
    });

    return response.ok;

    // Option 2: Use Nodemailer (if you prefer direct SMTP)
    /*
    const nodemailer = require('nodemailer');
    
    const transporter = nodemailer.createTransporter({
      service: 'gmail', // or your email service
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: 'New Contact Form Submission - Eclipse Softworks',
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Message:</strong></p>
        <p>${message}</p>
      `
    };

    await transporter.sendMail(mailOptions);
    return true;
    */

  } catch (error) {
    console.error('Email sending error:', error);
    return false;
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
