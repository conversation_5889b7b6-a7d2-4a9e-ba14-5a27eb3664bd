// Vercel Serverless Function for reCAPTCHA Verification
// This function runs on Vercel's servers and keeps your secret key secure

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      error: 'Method not allowed. Use POST.' 
    });
  }

  try {
    const { token } = req.body;

    // Validate that token is provided
    if (!token) {
      return res.status(400).json({ 
        success: false, 
        error: 'reCAPTCHA token is required' 
      });
    }

    // Get secret key from environment variables
    const secretKey = process.env.RECAPTCHA_SECRET_KEY;
    
    if (!secretKey) {
      console.error('RECAPTCHA_SECRET_KEY environment variable not set');
      return res.status(500).json({ 
        success: false, 
        error: 'Server configuration error' 
      });
    }

    // Verify the token with Google's reCAPTCHA API
    const verificationResponse = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${secretKey}&response=${token}`
    });

    const verificationData = await verificationResponse.json();

    // Log verification result for debugging (remove in production)
    console.log('reCAPTCHA verification result:', {
      success: verificationData.success,
      score: verificationData.score,
      action: verificationData.action,
      hostname: verificationData.hostname,
      challenge_ts: verificationData.challenge_ts
    });

    // Check if verification was successful
    if (verificationData.success) {
      return res.status(200).json({ 
        success: true, 
        message: 'reCAPTCHA verification successful',
        hostname: verificationData.hostname,
        challenge_ts: verificationData.challenge_ts
      });
    } else {
      // Log error codes for debugging
      console.error('reCAPTCHA verification failed:', verificationData['error-codes']);
      
      return res.status(400).json({ 
        success: false, 
        error: 'reCAPTCHA verification failed',
        errorCodes: verificationData['error-codes']
      });
    }

  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    
    return res.status(500).json({ 
      success: false, 
      error: 'Internal server error during verification' 
    });
  }
}

// CORS headers for cross-origin requests (if needed)
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
