// Backend reCAPTCHA Verification Example
// This code should run on your SERVER, not in the frontend

// Your reCAPTCHA Secret Key (KEEP THIS SECRET - NEVER put in frontend code)
const RECAPTCHA_SECRET_KEY = "YOUR_SECRET_KEY_HERE"; // Replace with your actual secret key

// Example 1: Node.js/Express endpoint
const express = require('express');
const app = express();

app.post('/api/contact', async (req, res) => {
  const { name, email, message, captchaToken } = req.body;

  // Verify reCAPTCHA token
  const isValidCaptcha = await verifyRecaptcha(captchaToken);
  
  if (!isValidCaptcha) {
    return res.status(400).json({ 
      success: false, 
      error: 'Invalid CAPTCHA verification' 
    });
  }

  // Process the form submission (send email, save to database, etc.)
  try {
    // Your email sending logic here
    await sendEmail({ name, email, message });
    
    res.json({ success: true, message: 'Email sent successfully' });
  } catch (error) {
    res.status(500).json({ 
      success: false, 
      error: 'Failed to send email' 
    });
  }
});

// reCAPTCHA verification function
async function verifyRecaptcha(token) {
  if (!token) return false;

  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `secret=${RECAPTCHA_SECRET_KEY}&response=${token}`
    });

    const data = await response.json();
    
    // Check if verification was successful
    return data.success === true;
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return false;
  }
}

// Example 2: PHP endpoint
/*
<?php
$secret_key = "YOUR_SECRET_KEY_HERE"; // Your secret key

if ($_POST['g-recaptcha-response']) {
    $captcha_token = $_POST['g-recaptcha-response'];
    
    // Verify with Google
    $response = file_get_contents("https://www.google.com/recaptcha/api/siteverify?secret=" . $secret_key . "&response=" . $captcha_token);
    $response_data = json_decode($response);
    
    if ($response_data->success) {
        // CAPTCHA verified - process form
        $name = $_POST['name'];
        $email = $_POST['email'];
        $message = $_POST['message'];
        
        // Send email logic here
        mail('<EMAIL>', 'Contact Form', $message);
        
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'error' => 'Invalid CAPTCHA']);
    }
}
?>
*/

// Example 3: Python/Flask endpoint
/*
from flask import Flask, request, jsonify
import requests

app = Flask(__name__)
RECAPTCHA_SECRET_KEY = "YOUR_SECRET_KEY_HERE"  # Your secret key

@app.route('/api/contact', methods=['POST'])
def contact():
    data = request.get_json()
    captcha_token = data.get('captchaToken')
    
    # Verify reCAPTCHA
    if not verify_recaptcha(captcha_token):
        return jsonify({'success': False, 'error': 'Invalid CAPTCHA'}), 400
    
    # Process form submission
    name = data.get('name')
    email = data.get('email')
    message = data.get('message')
    
    # Your email sending logic here
    send_email(name, email, message)
    
    return jsonify({'success': True})

def verify_recaptcha(token):
    if not token:
        return False
    
    response = requests.post('https://www.google.com/recaptcha/api/siteverify', {
        'secret': RECAPTCHA_SECRET_KEY,
        'response': token
    })
    
    result = response.json()
    return result.get('success', False)
*/

module.exports = { verifyRecaptcha };
